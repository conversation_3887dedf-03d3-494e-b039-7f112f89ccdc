class ViewOrdersManager {
  constructor() {
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.sortField = 'created_at';
    this.sortDirection = 'desc';
    this.filters = {
      search: '',
      status: '',
      payment: '',
      dateFrom: '',
      dateTo: ''
    };
    this.allOrders = [];
    this.filteredOrders = [];
    this.currentEditingOrder = null;
    this.currentStatusOrder = null;
    this.currentDebtOrder = null;
    
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadOrders();
    this.updateStatsSummary();
  }

  initializeEventListeners() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const clearSearchBtn = document.getElementById('clearSearchBtn');

    searchInput?.addEventListener('input', (e) => {
      this.filters.search = e.target.value;
      this.applyFilters();
      this.toggleClearSearchBtn();
    });

    searchBtn?.addEventListener('click', () => {
      this.applyFilters();
    });

    clearSearchBtn?.addEventListener('click', () => {
      searchInput.value = '';
      this.filters.search = '';
      this.applyFilters();
      this.toggleClearSearchBtn();
    });

    // الفلاتر
    document.getElementById('statusFilter')?.addEventListener('change', (e) => {
      this.filters.status = e.target.value;
      this.applyFilters();
    });

    document.getElementById('paymentFilter')?.addEventListener('change', (e) => {
      this.filters.payment = e.target.value;
      this.applyFilters();
    });

    document.getElementById('dateFromFilter')?.addEventListener('change', (e) => {
      this.filters.dateFrom = e.target.value;
      this.applyFilters();
    });

    document.getElementById('dateToFilter')?.addEventListener('change', (e) => {
      this.filters.dateTo = e.target.value;
      this.applyFilters();
    });

    // أزرار الإجراءات
    document.getElementById('exportExcelBtn')?.addEventListener('click', () => {
      this.exportToExcel();
    });

    document.getElementById('exportPdfBtn')?.addEventListener('click', () => {
      this.exportToPDF();
    });

    document.getElementById('printTableBtn')?.addEventListener('click', () => {
      this.printTable();
    });

    document.getElementById('refreshBtn')?.addEventListener('click', () => {
      this.loadOrders();
    });

    // التنقل بين الصفحات
    document.getElementById('itemsPerPage')?.addEventListener('change', (e) => {
      this.itemsPerPage = parseInt(e.target.value);
      this.currentPage = 1;
      this.renderTable();
    });

    // ترتيب الجدول
    document.querySelectorAll('.sortable').forEach(header => {
      header.addEventListener('click', () => {
        const field = header.dataset.sort;
        this.handleSort(field);
      });
    });

    // النوافذ المنبثقة
    this.initializeModals();
  }

  initializeModals() {
    // نافذة التفاصيل
    document.getElementById('closeDetailsModal')?.addEventListener('click', () => {
      this.hideModal('orderDetailsModal');
    });

    // نافذة التعديل
    document.getElementById('closeEditModal')?.addEventListener('click', () => {
      this.hideModal('editOrderModal');
    });

    document.getElementById('cancelEditBtn')?.addEventListener('click', () => {
      this.hideModal('editOrderModal');
    });

    document.getElementById('saveEditBtn')?.addEventListener('click', () => {
      this.saveOrderEdit();
    });

    // نافذة تحديث الحالة
    document.getElementById('closeStatusModal')?.addEventListener('click', () => {
      this.hideModal('updateStatusModal');
    });

    document.getElementById('cancelStatusBtn')?.addEventListener('click', () => {
      this.hideModal('updateStatusModal');
    });

    document.getElementById('saveStatusBtn')?.addEventListener('click', () => {
      this.saveStatusUpdate();
    });

    // نافذة تسديد الدين
    document.getElementById('closePayDebtModal')?.addEventListener('click', () => {
      this.hideModal('payDebtModal');
    });

    document.getElementById('cancelPaymentBtn')?.addEventListener('click', () => {
      this.hideModal('payDebtModal');
    });

    document.getElementById('confirmPaymentBtn')?.addEventListener('click', () => {
      this.confirmPayment();
    });

    // معالج تغيير الحالة
    document.querySelectorAll('input[name="new_status"]').forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.handleStatusChange(e.target.value);
      });
    });
  }

  async loadOrders() {
    try {
      this.showLoading();
      
      // تحميل الطلبات من قاعدة البيانات
      this.allOrders = simpleDatabase.getAll('repair_orders');
      
      // تحميل بيانات العملاء والشركات المصنعة
      const customers = simpleDatabase.getAll('customers');
      const manufacturers = simpleDatabase.getAll('manufacturers');
      
      // ربط البيانات
      this.allOrders = this.allOrders.map(order => {
        const customer = customers.find(c => c.id === order.customer_id);
        const manufacturer = manufacturers.find(m => m.id === order.manufacturer_id);
        
        return {
          ...order,
          customer_name: customer ? customer.name : 'غير محدد',
          customer_phone: customer ? customer.phone : '',
          manufacturer_name: manufacturer ? manufacturer.name : 'غير محدد'
        };
      });

      this.applyFilters();
      this.hideLoading();
      
    } catch (error) {
      console.error('خطأ في تحميل الطلبات:', error);
      this.showError('حدث خطأ في تحميل الطلبات');
      this.hideLoading();
    }
  }

  applyFilters() {
    let filtered = [...this.allOrders];

    // فلتر البحث
    if (this.filters.search) {
      const searchTerm = this.filters.search.toLowerCase();
      filtered = filtered.filter(order => 
        order.order_number?.toString().toLowerCase().includes(searchTerm) ||
        order.customer_name?.toLowerCase().includes(searchTerm) ||
        order.customer_phone?.toLowerCase().includes(searchTerm) ||
        order.phone_type?.toLowerCase().includes(searchTerm) ||
        order.problem_description?.toLowerCase().includes(searchTerm)
      );
    }

    // فلتر الحالة
    if (this.filters.status) {
      filtered = filtered.filter(order => order.status === this.filters.status);
    }

    // فلتر طريقة الدفع
    if (this.filters.payment) {
      filtered = filtered.filter(order => order.payment_method === this.filters.payment);
    }

    // فلتر التاريخ
    if (this.filters.dateFrom) {
      filtered = filtered.filter(order => 
        new Date(order.created_at) >= new Date(this.filters.dateFrom)
      );
    }

    if (this.filters.dateTo) {
      filtered = filtered.filter(order => 
        new Date(order.created_at) <= new Date(this.filters.dateTo + 'T23:59:59')
      );
    }

    this.filteredOrders = filtered;
    this.currentPage = 1;
    this.renderTable();
  }

  handleSort(field) {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }

    this.sortOrders();
    this.renderTable();
    this.updateSortIcons();
  }

  sortOrders() {
    this.filteredOrders.sort((a, b) => {
      let aValue = a[this.sortField];
      let bValue = b[this.sortField];

      // معالجة التواريخ
      if (this.sortField === 'created_at') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      // معالجة الأرقام
      if (typeof aValue === 'string' && !isNaN(aValue)) {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  renderTable() {
    const tbody = document.getElementById('ordersTableBody');
    const noDataMessage = document.getElementById('noDataMessage');
    
    if (!tbody) return;

    // حساب البيانات للصفحة الحالية
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageOrders = this.filteredOrders.slice(startIndex, endIndex);

    if (pageOrders.length === 0) {
      tbody.innerHTML = '';
      noDataMessage.style.display = 'block';
      return;
    }

    noDataMessage.style.display = 'none';

    tbody.innerHTML = pageOrders.map(order => `
      <tr>
        <td>
          <span class="order-number">#${order.order_number || order.id}</span>
        </td>
        <td>${order.customer_name}</td>
        <td>${order.phone_type}</td>
        <td>${this.formatDate(order.created_at)}</td>
        <td>
          <span class="status-badge status-${order.status || 'pending'}">
            ${this.getStatusText(order.status || 'pending')}
          </span>
        </td>
        <td>
          <span class="payment-method payment-${order.payment_method}">
            ${this.getPaymentMethodText(order.payment_method)}
          </span>
        </td>
        <td class="amount amount-paid">${(order.paid_amount || 0).toFixed(2)} ريال</td>
        <td class="amount amount-remaining">
          ${(order.remaining_amount || 0).toFixed(2)} ريال
          ${order.remaining_amount > 0 ? `
            <span class="debt-indicator" onclick="openPayDebtModal('${order.id}')">
              <i class="icon-money"></i>
              تسديد
            </span>
          ` : ''}
        </td>
        <td class="actions-cell">
          <button class="action-btn action-btn-view" onclick="viewOrderDetails('${order.id}')" title="عرض التفاصيل">
            <i class="icon-view"></i>
          </button>
          <button class="action-btn action-btn-edit" onclick="editOrder('${order.id}')" title="تعديل">
            <i class="icon-edit"></i>
          </button>
          <button class="action-btn action-btn-status" onclick="updateOrderStatus('${order.id}')" title="تحديث الحالة">
            <i class="icon-status"></i>
          </button>
          <button class="action-btn action-btn-print" onclick="printOrderReceipt('${order.id}')" title="طباعة الإيصال">
            <i class="icon-print"></i>
          </button>
          <button class="action-btn action-btn-delete" onclick="deleteOrder('${order.id}')" title="حذف">
            <i class="icon-delete"></i>
          </button>
        </td>
      </tr>
    `).join('');

    this.updatePagination();
  }

  updatePagination() {
    const totalRecords = this.filteredOrders.length;
    const totalPages = Math.ceil(totalRecords / this.itemsPerPage);
    const startRecord = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endRecord = Math.min(this.currentPage * this.itemsPerPage, totalRecords);

    // تحديث معلومات التنقل
    document.getElementById('showingFrom').textContent = totalRecords > 0 ? startRecord : 0;
    document.getElementById('showingTo').textContent = endRecord;
    document.getElementById('totalRecords').textContent = totalRecords;

    // إنشاء أزرار التنقل
    const paginationButtons = document.getElementById('paginationButtons');
    if (!paginationButtons) return;

    let buttonsHTML = '';

    // زر السابق
    buttonsHTML += `
      <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
              onclick="changePage(${this.currentPage - 1})">
        السابق
      </button>
    `;

    // أزرار الصفحات
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttonsHTML += `
        <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                onclick="changePage(${i})">
          ${i}
        </button>
      `;
    }

    // زر التالي
    buttonsHTML += `
      <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
              onclick="changePage(${this.currentPage + 1})">
        التالي
      </button>
    `;

    paginationButtons.innerHTML = buttonsHTML;
  }

  updateStatsSummary() {
    const totalOrders = this.allOrders.length;
    const pendingOrders = this.allOrders.filter(order =>
      ['pending', 'in-progress', 'waiting-parts'].includes(order.status)
    ).length;
    const completedOrders = this.allOrders.filter(order =>
      ['completed', 'delivered'].includes(order.status)
    ).length;

    document.getElementById('totalOrdersCount').textContent = totalOrders;
    document.getElementById('pendingOrdersCount').textContent = pendingOrders;
    document.getElementById('completedOrdersCount').textContent = completedOrders;
  }

  updateSortIcons() {
    document.querySelectorAll('.sortable').forEach(header => {
      const icon = header.querySelector('.sort-icon');
      header.classList.remove('sorted');

      if (header.dataset.sort === this.sortField) {
        header.classList.add('sorted');
        icon.textContent = this.sortDirection === 'asc' ? '↑' : '↓';
      } else {
        icon.textContent = '↕️';
      }
    });
  }

  toggleClearSearchBtn() {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
      clearBtn.style.display = this.filters.search ? 'block' : 'none';
    }
  }

  // دوال عرض التفاصيل
  async viewOrderDetails(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order) {
        this.showError('لم يتم العثور على الطلب');
        return;
      }

      const detailsContent = document.getElementById('orderDetailsContent');
      if (!detailsContent) return;

      detailsContent.innerHTML = `
        <div class="detail-section">
          <h4>معلومات الطلب</h4>
          <div class="detail-row">
            <span class="detail-label">رقم الطلب:</span>
            <span class="detail-value order-number">#${order.order_number || order.id}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">تاريخ التسجيل:</span>
            <span class="detail-value">${this.formatDate(order.created_at)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الحالة:</span>
            <span class="detail-value">
              <span class="status-badge status-${order.status || 'pending'}">
                ${this.getStatusText(order.status || 'pending')}
              </span>
            </span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الأولوية:</span>
            <span class="detail-value">${this.getPriorityText(order.priority)}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>بيانات العميل</h4>
          <div class="detail-row">
            <span class="detail-label">الاسم:</span>
            <span class="detail-value">${order.customer_name}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">رقم الهاتف:</span>
            <span class="detail-value">${order.customer_phone}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>تفاصيل الجهاز</h4>
          <div class="detail-row">
            <span class="detail-label">نوع الهاتف:</span>
            <span class="detail-value">${order.phone_type}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الشركة المصنعة:</span>
            <span class="detail-value">${order.manufacturer_name}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الطراز:</span>
            <span class="detail-value">${order.phone_model || 'غير محدد'}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">المشكلة:</span>
            <span class="detail-value">${order.problem_description}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>تفاصيل الدفع</h4>
          <div class="detail-row">
            <span class="detail-label">السعر الإجمالي:</span>
            <span class="detail-value amount">${(order.price || 0).toFixed(2)} ريال</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">طريقة الدفع:</span>
            <span class="detail-value">${this.getPaymentMethodText(order.payment_method)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">المبلغ المدفوع:</span>
            <span class="detail-value amount amount-paid">${(order.paid_amount || 0).toFixed(2)} ريال</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">المبلغ المتبقي:</span>
            <span class="detail-value amount amount-remaining">${(order.remaining_amount || 0).toFixed(2)} ريال</span>
          </div>
        </div>

        ${order.notes ? `
          <div class="detail-section">
            <h4>ملاحظات</h4>
            <div class="detail-row">
              <span class="detail-value">${order.notes}</span>
            </div>
          </div>
        ` : ''}

        <div class="detail-section">
          <h4>إجراءات سريعة</h4>
          <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button class="btn btn-primary" onclick="printOrderReceipt('${order.id}')">
              <i class="icon-print"></i>
              طباعة الإيصال
            </button>
            <button class="btn btn-warning" onclick="editOrder('${order.id}')">
              <i class="icon-edit"></i>
              تعديل الطلب
            </button>
            <button class="btn btn-info" onclick="updateOrderStatus('${order.id}')">
              <i class="icon-status"></i>
              تحديث الحالة
            </button>
            ${order.remaining_amount > 0 ? `
              <button class="btn btn-success" onclick="openPayDebtModal('${order.id}')">
                <i class="icon-money"></i>
                تسديد الدين
              </button>
            ` : ''}
          </div>
        </div>
      `;

      this.showModal('orderDetailsModal');

    } catch (error) {
      console.error('خطأ في عرض تفاصيل الطلب:', error);
      this.showError('حدث خطأ في عرض تفاصيل الطلب');
    }
  }

  // دوال التعديل
  async editOrder(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order) {
        this.showError('لم يتم العثور على الطلب');
        return;
      }

      this.currentEditingOrder = order;

      // تحميل الشركات المصنعة
      const manufacturers = simpleDatabase.getAll('manufacturers');

      const editForm = document.getElementById('editOrderForm');
      if (!editForm) return;

      editForm.innerHTML = `
        <div class="form-group">
          <label class="form-label">نوع الهاتف</label>
          <input type="text" id="editPhoneType" class="form-control" value="${order.phone_type}" required>
        </div>

        <div class="form-group">
          <label class="form-label">الشركة المصنعة</label>
          <select id="editManufacturer" class="form-control">
            <option value="">اختر الشركة المصنعة</option>
            ${manufacturers.map(m => `
              <option value="${m.id}" ${m.id == order.manufacturer_id ? 'selected' : ''}>
                ${m.name}
              </option>
            `).join('')}
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">وصف المشكلة</label>
          <textarea id="editProblem" class="form-control" rows="4" required>${order.problem_description}</textarea>
        </div>

        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">السعر</label>
              <div class="input-group">
                <input type="number" id="editPrice" class="form-control" value="${order.price}" min="0" step="0.01" required>
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">طريقة الدفع</label>
              <select id="editPaymentMethod" class="form-control">
                <option value="cash" ${order.payment_method === 'cash' ? 'selected' : ''}>نقداً</option>
                <option value="partial" ${order.payment_method === 'partial' ? 'selected' : ''}>جزئي</option>
                <option value="deferred" ${order.payment_method === 'deferred' ? 'selected' : ''}>آجل</option>
              </select>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">المبلغ المدفوع</label>
              <div class="input-group">
                <input type="number" id="editPaidAmount" class="form-control" value="${order.paid_amount || 0}" min="0" step="0.01">
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label class="form-label">الأولوية</label>
              <select id="editPriority" class="form-control">
                <option value="normal" ${order.priority === 'normal' ? 'selected' : ''}>عادي</option>
                <option value="urgent" ${order.priority === 'urgent' ? 'selected' : ''}>عاجل</option>
                <option value="very_urgent" ${order.priority === 'very_urgent' ? 'selected' : ''}>عاجل جداً</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">ملاحظات</label>
          <textarea id="editNotes" class="form-control" rows="3">${order.notes || ''}</textarea>
        </div>
      `;

      this.showModal('editOrderModal');

    } catch (error) {
      console.error('خطأ في تحميل نموذج التعديل:', error);
      this.showError('حدث خطأ في تحميل نموذج التعديل');
    }
  }

  async saveOrderEdit() {
    try {
      if (!this.currentEditingOrder) return;

      const updatedData = {
        phone_type: document.getElementById('editPhoneType').value,
        manufacturer_id: document.getElementById('editManufacturer').value,
        problem_description: document.getElementById('editProblem').value,
        price: parseFloat(document.getElementById('editPrice').value),
        payment_method: document.getElementById('editPaymentMethod').value,
        paid_amount: parseFloat(document.getElementById('editPaidAmount').value || 0),
        priority: document.getElementById('editPriority').value,
        notes: document.getElementById('editNotes').value
      };

      // حساب المبلغ المتبقي
      updatedData.remaining_amount = updatedData.price - updatedData.paid_amount;

      // التحقق من صحة البيانات
      if (!updatedData.phone_type || !updatedData.problem_description || !updatedData.price) {
        this.showError('يرجى ملء جميع الحقول المطلوبة');
        return;
      }

      if (updatedData.paid_amount > updatedData.price) {
        this.showError('المبلغ المدفوع لا يمكن أن يكون أكبر من السعر الإجمالي');
        return;
      }

      // حفظ التعديلات
      const result = simpleDatabase.update('repair_orders', this.currentEditingOrder.id, updatedData);

      if (result.changes > 0) {
        // تسجيل النشاط
        simpleDatabase.logActivity('update', 'repair_orders', this.currentEditingOrder.id,
          this.currentEditingOrder, updatedData);

        this.showSuccess('تم حفظ التعديلات بنجاح');
        this.hideModal('editOrderModal');
        await this.loadOrders();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في حفظ التعديلات');
      }

    } catch (error) {
      console.error('خطأ في حفظ التعديلات:', error);
      this.showError('حدث خطأ في حفظ التعديلات');
    }
  }

  // دوال تحديث الحالة
  async updateOrderStatus(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order) {
        this.showError('لم يتم العثور على الطلب');
        return;
      }

      this.currentStatusOrder = order;

      // إعادة تعيين النموذج
      document.querySelectorAll('input[name="new_status"]').forEach(radio => {
        radio.checked = false;
      });

      document.getElementById('statusSpecificFields').innerHTML = '';

      this.showModal('updateStatusModal');

    } catch (error) {
      console.error('خطأ في فتح نافذة تحديث الحالة:', error);
      this.showError('حدث خطأ في فتح نافذة تحديث الحالة');
    }
  }

  handleStatusChange(newStatus) {
    const statusFields = document.getElementById('statusSpecificFields');
    if (!statusFields) return;

    let fieldsHTML = '';

    switch (newStatus) {
      case 'failed':
        fieldsHTML = `
          <div class="form-group">
            <label class="form-label">سبب فشل الصيانة *</label>
            <textarea id="failureReason" class="form-control" rows="3"
                      placeholder="اكتب سبب فشل الصيانة..." required></textarea>
          </div>
        `;
        break;

      case 'waiting-parts':
        fieldsHTML = `
          <div class="form-group">
            <label class="form-label">قطع الغيار المطلوبة *</label>
            <textarea id="requiredParts" class="form-control" rows="3"
                      placeholder="اكتب أسماء قطع الغيار المطلوبة..." required></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">التاريخ المتوقع للوصول</label>
            <input type="date" id="expectedArrival" class="form-control">
          </div>
        `;
        break;

      case 'delivered':
        fieldsHTML = `
          <div class="form-group">
            <label class="form-label">هل تم الدفع؟</label>
            <div class="payment-status-options">
              <label class="radio-option">
                <input type="radio" name="payment_status" value="paid">
                <span class="radio-text">تم الدفع كاملاً</span>
              </label>
              <label class="radio-option">
                <input type="radio" name="payment_status" value="partial">
                <span class="radio-text">دفع جزئي</span>
              </label>
              <label class="radio-option">
                <input type="radio" name="payment_status" value="not_paid">
                <span class="radio-text">لم يتم الدفع</span>
              </label>
            </div>
          </div>

          <div id="partialPaymentFields" style="display: none;">
            <div class="form-group">
              <label class="form-label">المبلغ المدفوع الإضافي</label>
              <div class="input-group">
                <input type="number" id="additionalPayment" class="form-control" min="0" step="0.01">
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">اسم المستلم</label>
            <input type="text" id="receiverName" class="form-control" placeholder="اسم الشخص الذي استلم الجهاز">
          </div>

          <div class="form-group">
            <label class="form-label">تاريخ التسليم</label>
            <input type="datetime-local" id="deliveryDate" class="form-control">
          </div>

          <div class="form-group">
            <label class="form-label">ملاحظات التسليم</label>
            <textarea id="deliveryNotes" class="form-control" rows="2"
                      placeholder="أي ملاحظات خاصة بالتسليم..."></textarea>
          </div>
        `;
        break;

      case 'completed':
        fieldsHTML = `
          <div class="form-group">
            <label class="form-label">ملاحظات الإنجاز</label>
            <textarea id="completionNotes" class="form-control" rows="3"
                      placeholder="ملاحظات حول إنجاز الصيانة..."></textarea>
          </div>
          <div class="form-group">
            <label class="form-label">تاريخ الإنجاز</label>
            <input type="datetime-local" id="completionDate" class="form-control">
          </div>
        `;
        break;

      case 'in-progress':
        fieldsHTML = `
          <div class="form-group">
            <label class="form-label">ملاحظات بدء الصيانة</label>
            <textarea id="progressNotes" class="form-control" rows="2"
                      placeholder="ملاحظات حول بدء عملية الصيانة..."></textarea>
          </div>
        `;
        break;
    }

    statusFields.innerHTML = fieldsHTML;

    // إضافة معالجات الأحداث للحقول الجديدة
    if (newStatus === 'delivered') {
      document.querySelectorAll('input[name="payment_status"]').forEach(radio => {
        radio.addEventListener('change', (e) => {
          const partialFields = document.getElementById('partialPaymentFields');
          if (partialFields) {
            partialFields.style.display = e.target.value === 'partial' ? 'block' : 'none';
          }
        });
      });

      // تعيين التاريخ الحالي
      const deliveryDate = document.getElementById('deliveryDate');
      if (deliveryDate) {
        deliveryDate.value = new Date().toISOString().slice(0, 16);
      }
    }

    if (newStatus === 'completed') {
      const completionDate = document.getElementById('completionDate');
      if (completionDate) {
        completionDate.value = new Date().toISOString().slice(0, 16);
      }
    }
  }

  async saveStatusUpdate() {
    try {
      if (!this.currentStatusOrder) return;

      const newStatus = document.querySelector('input[name="new_status"]:checked')?.value;
      if (!newStatus) {
        this.showError('يرجى اختيار الحالة الجديدة');
        return;
      }

      const updateData = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      // جمع البيانات الإضافية حسب الحالة
      let additionalData = {};

      switch (newStatus) {
        case 'failed':
          const failureReason = document.getElementById('failureReason')?.value;
          if (!failureReason) {
            this.showError('يرجى إدخال سبب فشل الصيانة');
            return;
          }
          additionalData.failure_reason = failureReason;
          break;

        case 'waiting-parts':
          const requiredParts = document.getElementById('requiredParts')?.value;
          if (!requiredParts) {
            this.showError('يرجى إدخال قطع الغيار المطلوبة');
            return;
          }
          additionalData.required_parts = requiredParts;
          additionalData.expected_arrival = document.getElementById('expectedArrival')?.value;
          break;

        case 'delivered':
          const paymentStatus = document.querySelector('input[name="payment_status"]:checked')?.value;
          if (!paymentStatus) {
            this.showError('يرجى تحديد حالة الدفع');
            return;
          }

          additionalData.payment_status = paymentStatus;
          additionalData.receiver_name = document.getElementById('receiverName')?.value;
          additionalData.delivery_date = document.getElementById('deliveryDate')?.value;
          additionalData.delivery_notes = document.getElementById('deliveryNotes')?.value;

          // معالجة الدفع
          if (paymentStatus === 'paid') {
            updateData.paid_amount = this.currentStatusOrder.price;
            updateData.remaining_amount = 0;
          } else if (paymentStatus === 'partial') {
            const additionalPayment = parseFloat(document.getElementById('additionalPayment')?.value || 0);
            updateData.paid_amount = (this.currentStatusOrder.paid_amount || 0) + additionalPayment;
            updateData.remaining_amount = this.currentStatusOrder.price - updateData.paid_amount;
          }
          // إذا لم يتم الدفع، تبقى المبالغ كما هي
          break;

        case 'completed':
          additionalData.completion_notes = document.getElementById('completionNotes')?.value;
          additionalData.completion_date = document.getElementById('completionDate')?.value;
          break;

        case 'in-progress':
          additionalData.progress_notes = document.getElementById('progressNotes')?.value;
          break;
      }

      // دمج البيانات الإضافية
      Object.assign(updateData, additionalData);

      // حفظ التحديث
      const result = simpleDatabase.update('repair_orders', this.currentStatusOrder.id, updateData);

      if (result.changes > 0) {
        // تسجيل النشاط
        simpleDatabase.logActivity('status_update', 'repair_orders', this.currentStatusOrder.id,
          { status: this.currentStatusOrder.status }, { status: newStatus, ...additionalData });

        this.showSuccess(`تم تحديث حالة الطلب إلى "${this.getStatusText(newStatus)}" بنجاح`);
        this.hideModal('updateStatusModal');
        await this.loadOrders();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في تحديث الحالة');
      }

    } catch (error) {
      console.error('خطأ في تحديث الحالة:', error);
      this.showError('حدث خطأ في تحديث الحالة');
    }
  }

  // دوال تسديد الديون
  async openPayDebtModal(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order || order.remaining_amount <= 0) {
        this.showError('لا يوجد دين على هذا الطلب');
        return;
      }

      this.currentDebtOrder = order;

      const debtInfo = document.getElementById('debtInfo');
      if (debtInfo) {
        debtInfo.innerHTML = `
          <h4>معلومات الدين</h4>
          <p><strong>رقم الطلب:</strong> #${order.order_number || order.id}</p>
          <p><strong>اسم العميل:</strong> ${order.customer_name}</p>
          <p><strong>السعر الإجمالي:</strong> ${order.price.toFixed(2)} ريال</p>
          <p><strong>المبلغ المدفوع:</strong> ${(order.paid_amount || 0).toFixed(2)} ريال</p>
          <div class="debt-amount">المبلغ المتبقي: ${order.remaining_amount.toFixed(2)} ريال</div>
        `;
      }

      // تعيين الحد الأقصى للمبلغ
      const paymentAmount = document.getElementById('paymentAmount');
      if (paymentAmount) {
        paymentAmount.max = order.remaining_amount;
        paymentAmount.value = order.remaining_amount;
      }

      this.showModal('payDebtModal');

    } catch (error) {
      console.error('خطأ في فتح نافذة تسديد الدين:', error);
      this.showError('حدث خطأ في فتح نافذة تسديد الدين');
    }
  }

  async confirmPayment() {
    try {
      if (!this.currentDebtOrder) return;

      const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
      const paymentNotes = document.getElementById('paymentNotes').value;

      if (!paymentAmount || paymentAmount <= 0) {
        this.showError('يرجى إدخال مبلغ صحيح');
        return;
      }

      if (paymentAmount > this.currentDebtOrder.remaining_amount) {
        this.showError('المبلغ المدخل أكبر من المبلغ المتبقي');
        return;
      }

      // تحديث بيانات الطلب
      const newPaidAmount = (this.currentDebtOrder.paid_amount || 0) + paymentAmount;
      const newRemainingAmount = this.currentDebtOrder.price - newPaidAmount;

      const updateData = {
        paid_amount: newPaidAmount,
        remaining_amount: newRemainingAmount,
        updated_at: new Date().toISOString()
      };

      // إذا تم تسديد المبلغ كاملاً، تحديث طريقة الدفع
      if (newRemainingAmount === 0) {
        updateData.payment_method = 'cash';
      }

      // حفظ التحديث
      const result = simpleDatabase.update('repair_orders', this.currentDebtOrder.id, updateData);

      if (result.changes > 0) {
        // تسجيل عملية الدفع
        const paymentRecord = {
          order_id: this.currentDebtOrder.id,
          customer_id: this.currentDebtOrder.customer_id,
          amount: paymentAmount,
          notes: paymentNotes,
          payment_date: new Date().toISOString()
        };

        simpleDatabase.insert('payments', paymentRecord);

        // تسجيل النشاط
        simpleDatabase.logActivity('payment', 'repair_orders', this.currentDebtOrder.id,
          null, { amount: paymentAmount, remaining: newRemainingAmount });

        this.showSuccess(`تم تسديد ${paymentAmount.toFixed(2)} ريال بنجاح`);
        this.hideModal('payDebtModal');
        await this.loadOrders();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في تسجيل الدفع');
      }

    } catch (error) {
      console.error('خطأ في تسجيل الدفع:', error);
      this.showError('حدث خطأ في تسجيل الدفع');
    }
  }

  // دوال الحذف
  async deleteOrder(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order) {
        this.showError('لم يتم العثور على الطلب');
        return;
      }

      const confirmed = confirm(`هل أنت متأكد من حذف طلب الصيانة #${order.order_number || order.id}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);

      if (!confirmed) return;

      // حذف الطلب
      const result = simpleDatabase.delete('repair_orders', orderId);

      if (result.changes > 0) {
        // تسجيل النشاط
        simpleDatabase.logActivity('delete', 'repair_orders', orderId, order, null);

        this.showSuccess('تم حذف الطلب بنجاح');
        await this.loadOrders();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في حذف الطلب');
      }

    } catch (error) {
      console.error('خطأ في حذف الطلب:', error);
      this.showError('حدث خطأ في حذف الطلب');
    }
  }

  // دوال الطباعة والتصدير
  async printOrderReceipt(orderId) {
    try {
      const order = this.allOrders.find(o => o.id == orderId);
      if (!order) {
        this.showError('لم يتم العثور على الطلب');
        return;
      }

      // إنشاء نافذة جديدة للطباعة
      const printWindow = window.open('', '_blank');

      // إنشاء محتوى الإيصال
      const receiptHTML = await this.generateReceiptHTML(order);

      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>إيصال صيانة #${order.order_number || order.id}</title>
          <style>
            body { font-family: 'Cairo', Arial, sans-serif; direction: rtl; margin: 0; padding: 20px; }
            .receipt { max-width: 210mm; margin: 0 auto; }
            @media print { body { margin: 0; } .receipt { max-width: none; } }
          </style>
        </head>
        <body>
          <div class="receipt">${receiptHTML}</div>
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

    } catch (error) {
      console.error('خطأ في طباعة الإيصال:', error);
      this.showError('حدث خطأ في طباعة الإيصال');
    }
  }

  async generateReceiptHTML(order) {
    // الحصول على إعدادات المركز
    const centerName = simpleDatabase.getSetting('center_name') || 'مركز صيانة الموبايلات';
    const centerAddress = simpleDatabase.getSetting('center_address') || 'العنوان';
    const centerPhone = simpleDatabase.getSetting('center_phone') || 'رقم الهاتف';

    return `
      <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #2563eb; padding-bottom: 20px;">
        <h1 style="color: #2563eb; margin: 0;">${centerName}</h1>
        <p style="margin: 5px 0;">${centerAddress}</p>
        <p style="margin: 5px 0;">${centerPhone}</p>
      </div>

      <div style="text-align: center; margin-bottom: 25px;">
        <h2 style="background: #f3f4f6; padding: 10px; border-radius: 8px; margin: 0;">إيصال صيانة جهاز</h2>
      </div>

      <div style="margin-bottom: 20px; padding: 15px; background: #f9fafb; border-radius: 8px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
          <span><strong>رقم الطلب:</strong> <span style="color: #dc2626; font-weight: bold;">#${order.order_number || order.id}</span></span>
          <span><strong>التاريخ:</strong> ${this.formatDate(order.created_at)}</span>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="background: #2563eb; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">بيانات العميل</h3>
        <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
          <p><strong>الاسم:</strong> ${order.customer_name}</p>
          <p><strong>الهاتف:</strong> ${order.customer_phone}</p>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="background: #2563eb; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">تفاصيل الجهاز</h3>
        <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
          <p><strong>نوع الهاتف:</strong> ${order.phone_type}</p>
          <p><strong>المشكلة:</strong> ${order.problem_description}</p>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <h3 style="background: #2563eb; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">تفاصيل الدفع</h3>
        <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
          <p><strong>السعر الإجمالي:</strong> ${order.price.toFixed(2)} ريال</p>
          <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(order.payment_method)}</p>
          <p><strong>المبلغ المدفوع:</strong> ${(order.paid_amount || 0).toFixed(2)} ريال</p>
          <p><strong>المبلغ المتبقي:</strong> ${(order.remaining_amount || 0).toFixed(2)} ريال</p>
        </div>
      </div>

      ${order.notes ? `
        <div style="margin-bottom: 20px;">
          <h3 style="background: #2563eb; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">ملاحظات</h3>
          <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
            <p>${order.notes}</p>
          </div>
        </div>
      ` : ''}

      <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
        <p style="color: #2563eb; font-weight: bold;">شكراً لزيارتكم مركزنا</p>
        <p style="color: #6b7280;">نتشرف بخدمتكم دائماً</p>
        <p style="font-size: 12px; color: #9ca3af;">تم إنشاء هذا الإيصال في: ${new Date().toLocaleString('ar-SA')}</p>
      </div>
    `;
  }

  async exportToExcel() {
    try {
      if (typeof XLSX === 'undefined') {
        this.showError('مكتبة Excel غير متوفرة');
        return;
      }

      const data = this.filteredOrders.map(order => ({
        'رقم الطلب': order.order_number || order.id,
        'اسم العميل': order.customer_name,
        'رقم الهاتف': order.customer_phone,
        'نوع الهاتف': order.phone_type,
        'المشكلة': order.problem_description,
        'تاريخ التسجيل': this.formatDate(order.created_at),
        'الحالة': this.getStatusText(order.status),
        'طريقة الدفع': this.getPaymentMethodText(order.payment_method),
        'السعر': order.price,
        'المدفوع': order.paid_amount || 0,
        'المتبقي': order.remaining_amount || 0,
        'ملاحظات': order.notes || ''
      }));

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'طلبات الصيانة');

      const fileName = `طلبات_الصيانة_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      this.showSuccess('تم تصدير البيانات إلى Excel بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      this.showError('حدث خطأ في تصدير البيانات');
    }
  }

  async exportToPDF() {
    try {
      if (typeof jsPDF === 'undefined') {
        this.showError('مكتبة PDF غير متوفرة');
        return;
      }

      const pdf = new jsPDF('p', 'mm', 'a4');

      // إضافة عنوان
      pdf.setFont('Arial', 'bold');
      pdf.setFontSize(16);
      pdf.text('تقرير طلبات الصيانة', 105, 20, { align: 'center' });

      // إضافة تاريخ التقرير
      pdf.setFontSize(10);
      pdf.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}`, 105, 30, { align: 'center' });

      // إضافة الجدول
      const tableData = this.filteredOrders.map(order => [
        order.order_number || order.id,
        order.customer_name,
        order.phone_type,
        this.formatDate(order.created_at),
        this.getStatusText(order.status),
        `${(order.paid_amount || 0).toFixed(2)} ريال`,
        `${(order.remaining_amount || 0).toFixed(2)} ريال`
      ]);

      const headers = ['رقم الطلب', 'العميل', 'نوع الهاتف', 'التاريخ', 'الحالة', 'المدفوع', 'المتبقي'];

      // استخدام autoTable إذا كانت متوفرة
      if (typeof pdf.autoTable === 'function') {
        pdf.autoTable({
          head: [headers],
          body: tableData,
          startY: 40,
          styles: { fontSize: 8, cellPadding: 2 },
          headStyles: { fillColor: [37, 99, 235] }
        });
      }

      const fileName = `طلبات_الصيانة_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      this.showSuccess('تم تصدير التقرير إلى PDF بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      this.showError('حدث خطأ في تصدير التقرير');
    }
  }

  printTable() {
    try {
      const printContent = `
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>طلبات الصيانة</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
            h1 { text-align: center; color: #2563eb; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
            th { background-color: #f3f4f6; font-weight: bold; }
            .status { padding: 4px 8px; border-radius: 12px; font-size: 11px; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <h1>تقرير طلبات الصيانة</h1>
          <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
          <p>عدد الطلبات: ${this.filteredOrders.length}</p>

          <table>
            <thead>
              <tr>
                <th>رقم الطلب</th>
                <th>اسم العميل</th>
                <th>نوع الهاتف</th>
                <th>تاريخ التسجيل</th>
                <th>الحالة</th>
                <th>المدفوع</th>
                <th>المتبقي</th>
              </tr>
            </thead>
            <tbody>
              ${this.filteredOrders.map(order => `
                <tr>
                  <td>#${order.order_number || order.id}</td>
                  <td>${order.customer_name}</td>
                  <td>${order.phone_type}</td>
                  <td>${this.formatDate(order.created_at)}</td>
                  <td class="status">${this.getStatusText(order.status)}</td>
                  <td>${(order.paid_amount || 0).toFixed(2)} ريال</td>
                  <td>${(order.remaining_amount || 0).toFixed(2)} ريال</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
        </html>
      `;

      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();

    } catch (error) {
      console.error('خطأ في طباعة الجدول:', error);
      this.showError('حدث خطأ في طباعة الجدول');
    }
  }

  // دوال مساعدة
  formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return 'تاريخ غير صحيح';
    }
  }

  getStatusText(status) {
    const statusTexts = {
      'pending': 'قيد الانتظار',
      'in-progress': 'قيد الصيانة',
      'completed': 'مكتمل',
      'failed': 'فشل الصيانة',
      'waiting-parts': 'بانتظار قطع الغيار',
      'delivered': 'تم التسليم'
    };
    return statusTexts[status] || status;
  }

  getPaymentMethodText(method) {
    const methods = {
      'cash': 'نقداً',
      'partial': 'جزئي',
      'deferred': 'آجل'
    };
    return methods[method] || method;
  }

  getPriorityText(priority) {
    const priorities = {
      'normal': 'عادي',
      'urgent': 'عاجل',
      'very_urgent': 'عاجل جداً'
    };
    return priorities[priority] || 'عادي';
  }

  // دوال النوافذ المنبثقة
  showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'none';
      document.body.style.overflow = 'auto';
    }
  }

  // دوال الإشعارات
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="notification-icon icon-${type === 'success' ? 'check' : type === 'error' ? 'warning' : 'info'}"></i>
        <span class="notification-text">${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="icon-close"></i>
      </button>
    `;

    // إضافة الأنماط إذا لم تكن موجودة
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          padding: 16px;
          display: flex;
          align-items: center;
          gap: 12px;
          z-index: 10000;
          animation: slideInRight 0.3s ease-out;
          max-width: 400px;
        }
        .notification-success { border-left: 4px solid #10b981; }
        .notification-error { border-left: 4px solid #ef4444; }
        .notification-info { border-left: 4px solid #3b82f6; }
        .notification-content { display: flex; align-items: center; gap: 8px; flex: 1; }
        .notification-icon { font-size: 18px; }
        .notification-success .notification-icon { color: #10b981; }
        .notification-error .notification-icon { color: #ef4444; }
        .notification-close { background: none; border: none; cursor: pointer; color: #6b7280; }
        @keyframes slideInRight { from { transform: translateX(100%); } to { transform: translateX(0); } }
      `;
      document.head.appendChild(style);
    }

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  showLoading() {
    const tbody = document.getElementById('ordersTableBody');
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="9" style="text-align: center; padding: 40px;">
            <div class="loading-spinner"></div>
            <p>جاري تحميل البيانات...</p>
          </td>
        </tr>
      `;
    }
  }

  hideLoading() {
    // سيتم إخفاء التحميل عند عرض البيانات
  }
}

// دوال عامة للوصول من HTML
window.viewOrderDetails = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.viewOrderDetails(orderId);
  }
};

window.editOrder = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.editOrder(orderId);
  }
};

window.updateOrderStatus = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.updateOrderStatus(orderId);
  }
};

window.printOrderReceipt = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.printOrderReceipt(orderId);
  }
};

window.deleteOrder = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.deleteOrder(orderId);
  }
};

window.openPayDebtModal = function(orderId) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.openPayDebtModal(orderId);
  }
};

window.changePage = function(page) {
  if (window.viewOrdersManager) {
    window.viewOrdersManager.currentPage = page;
    window.viewOrdersManager.renderTable();
  }
};

window.clearFilters = function() {
  if (window.viewOrdersManager) {
    // مسح جميع الفلاتر
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('paymentFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';

    // إعادة تعيين الفلاتر
    window.viewOrdersManager.filters = {
      search: '',
      status: '',
      payment: '',
      dateFrom: '',
      dateTo: ''
    };

    // تطبيق الفلاتر
    window.viewOrdersManager.applyFilters();
    window.viewOrdersManager.toggleClearSearchBtn();
  }
};

// تصدير الكلاس
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ViewOrdersManager;
} else {
  window.ViewOrdersManager = ViewOrdersManager;
}
