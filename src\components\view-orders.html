<div class="view-orders-container">
  <!-- رأس الصفحة -->
  <div class="page-header">
    <div class="header-content">
      <div class="header-left">
        <div class="header-icon">
          <i class="icon-list"></i>
        </div>
        <div class="header-text">
          <h2 class="page-title">عرض طلبات الصيانة</h2>
          <p class="page-subtitle">إدارة ومتابعة جميع طلبات الصيانة المسجلة في النظام</p>
        </div>
      </div>
      <div class="header-right">
        <div class="stats-summary">
          <div class="stat-item">
            <span class="stat-number" id="totalOrdersCount">0</span>
            <span class="stat-label">إجمالي الطلبات</span>
          </div>
          <div class="stat-item">
            <span class="stat-number" id="pendingOrdersCount">0</span>
            <span class="stat-label">قيد المعالجة</span>
          </div>
          <div class="stat-item">
            <span class="stat-number" id="completedOrdersCount">0</span>
            <span class="stat-label">مكتملة</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- شريط الأدوات -->
  <div class="toolbar">
    <div class="toolbar-left">
      <!-- البحث -->
      <div class="search-container">
        <input type="text" id="searchInput" class="search-input" 
               placeholder="البحث برقم الطلب، اسم العميل، رقم الهاتف، نوع الجهاز، أو الباركود...">
        <button class="search-btn" id="searchBtn">
          <i class="icon-search"></i>
        </button>
        <button class="clear-search-btn" id="clearSearchBtn" style="display: none;">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>
    
    <div class="toolbar-center">
      <!-- فلاتر -->
      <div class="filters">
        <select id="statusFilter" class="filter-select">
          <option value="">جميع الحالات</option>
          <option value="pending">قيد الانتظار</option>
          <option value="in-progress">قيد الصيانة</option>
          <option value="completed">مكتمل</option>
          <option value="failed">فشل الصيانة</option>
          <option value="waiting-parts">بانتظار قطع الغيار</option>
          <option value="delivered">تم التسليم</option>
        </select>
        
        <select id="paymentFilter" class="filter-select">
          <option value="">جميع طرق الدفع</option>
          <option value="cash">نقداً</option>
          <option value="partial">جزئي</option>
          <option value="deferred">آجل</option>
        </select>
        
        <input type="date" id="dateFromFilter" class="filter-input" placeholder="من تاريخ">
        <input type="date" id="dateToFilter" class="filter-input" placeholder="إلى تاريخ">
      </div>
    </div>
    
    <div class="toolbar-right">
      <!-- أزرار الإجراءات -->
      <div class="action-buttons">
        <button class="btn btn-success" id="exportExcelBtn">
          <i class="icon-excel"></i>
          تصدير Excel
        </button>
        <button class="btn btn-danger" id="exportPdfBtn">
          <i class="icon-pdf"></i>
          تصدير PDF
        </button>
        <button class="btn btn-info" id="printTableBtn">
          <i class="icon-print"></i>
          طباعة الجدول
        </button>
        <button class="btn btn-primary" id="refreshBtn">
          <i class="icon-refresh"></i>
          تحديث
        </button>
      </div>
    </div>
  </div>

  <!-- الجدول الرئيسي -->
  <div class="table-container">
    <div class="table-wrapper">
      <table class="orders-table" id="ordersTable">
        <thead>
          <tr>
            <th class="sortable" data-sort="order_number">
              رقم الطلب
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="customer_name">
              اسم العميل
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="phone_type">
              نوع الهاتف
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="created_at">
              تاريخ التسجيل
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="status">
              الحالة
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="payment_method">
              طريقة الدفع
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="paid_amount">
              المدفوع
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="sortable" data-sort="remaining_amount">
              المتبقي
              <i class="sort-icon icon-sort"></i>
            </th>
            <th class="actions-header">الإجراءات</th>
          </tr>
        </thead>
        <tbody id="ordersTableBody">
          <!-- سيتم تحميل البيانات هنا -->
        </tbody>
      </table>
    </div>
    
    <!-- رسالة عدم وجود بيانات -->
    <div class="no-data" id="noDataMessage" style="display: none;">
      <div class="no-data-icon">
        <i class="icon-empty"></i>
      </div>
      <h3>لا توجد طلبات</h3>
      <p>لم يتم العثور على أي طلبات صيانة مطابقة للبحث</p>
      <button class="btn btn-primary" onclick="clearFilters()">
        <i class="icon-refresh"></i>
        مسح الفلاتر
      </button>
    </div>
  </div>

  <!-- التنقل بين الصفحات -->
  <div class="pagination-container">
    <div class="pagination-info">
      <span>عرض <span id="showingFrom">1</span> إلى <span id="showingTo">10</span> من <span id="totalRecords">0</span> طلب</span>
    </div>
    <div class="pagination-controls">
      <select id="itemsPerPage" class="items-per-page">
        <option value="10">10 لكل صفحة</option>
        <option value="25">25 لكل صفحة</option>
        <option value="50">50 لكل صفحة</option>
        <option value="100">100 لكل صفحة</option>
      </select>
      <div class="pagination-buttons" id="paginationButtons">
        <!-- أزرار التنقل -->
      </div>
    </div>
  </div>
</div>

<!-- نافذة عرض التفاصيل -->
<div class="modal" id="orderDetailsModal" style="display: none;">
  <div class="modal-content modal-lg">
    <div class="modal-header">
      <h3>تفاصيل طلب الصيانة</h3>
      <button type="button" class="close-modal" id="closeDetailsModal">
        <i class="icon-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="order-details" id="orderDetailsContent">
        <!-- سيتم تحميل التفاصيل هنا -->
      </div>
    </div>
  </div>
</div>

<!-- نافذة التعديل -->
<div class="modal" id="editOrderModal" style="display: none;">
  <div class="modal-content modal-lg">
    <div class="modal-header">
      <h3>تعديل طلب الصيانة</h3>
      <button type="button" class="close-modal" id="closeEditModal">
        <i class="icon-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <form id="editOrderForm">
        <!-- نموذج التعديل -->
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" id="saveEditBtn">
        <i class="icon-save"></i>
        حفظ التعديلات
      </button>
      <button type="button" class="btn btn-secondary" id="cancelEditBtn">
        إلغاء
      </button>
    </div>
  </div>
</div>

<!-- نافذة تحديث الحالة -->
<div class="modal" id="updateStatusModal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <h3>تحديث حالة الطلب</h3>
      <button type="button" class="close-modal" id="closeStatusModal">
        <i class="icon-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <form id="updateStatusForm">
        <div class="form-group">
          <label class="form-label">الحالة الجديدة</label>
          <div class="status-options">
            <label class="status-option">
              <input type="radio" name="new_status" value="in-progress">
              <span class="status-badge status-in-progress">قيد الصيانة</span>
            </label>
            <label class="status-option">
              <input type="radio" name="new_status" value="completed">
              <span class="status-badge status-completed">مكتمل</span>
            </label>
            <label class="status-option">
              <input type="radio" name="new_status" value="failed">
              <span class="status-badge status-failed">فشل الصيانة</span>
            </label>
            <label class="status-option">
              <input type="radio" name="new_status" value="waiting-parts">
              <span class="status-badge status-waiting-parts">بانتظار قطع الغيار</span>
            </label>
            <label class="status-option">
              <input type="radio" name="new_status" value="delivered">
              <span class="status-badge status-delivered">تم التسليم</span>
            </label>
          </div>
        </div>
        
        <!-- حقول إضافية حسب الحالة -->
        <div id="statusSpecificFields">
          <!-- سيتم إضافة الحقول ديناميكياً -->
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" id="saveStatusBtn">
        <i class="icon-save"></i>
        تحديث الحالة
      </button>
      <button type="button" class="btn btn-secondary" id="cancelStatusBtn">
        إلغاء
      </button>
    </div>
  </div>
</div>

<!-- نافذة تسديد الدين -->
<div class="modal" id="payDebtModal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <h3>تسديد دين العميل</h3>
      <button type="button" class="close-modal" id="closePayDebtModal">
        <i class="icon-close"></i>
      </button>
    </div>
    <div class="modal-body">
      <div class="debt-info" id="debtInfo">
        <!-- معلومات الدين -->
      </div>
      <form id="payDebtForm">
        <div class="form-group">
          <label for="paymentAmount" class="form-label">المبلغ المراد تسديده</label>
          <div class="input-group">
            <input type="number" id="paymentAmount" class="form-control" min="0" step="0.01" required>
            <span class="input-group-text">ريال</span>
          </div>
        </div>
        <div class="form-group">
          <label for="paymentNotes" class="form-label">ملاحظات الدفع (اختياري)</label>
          <textarea id="paymentNotes" class="form-control" rows="3"></textarea>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-success" id="confirmPaymentBtn">
        <i class="icon-money"></i>
        تأكيد التسديد
      </button>
      <button type="button" class="btn btn-secondary" id="cancelPaymentBtn">
        إلغاء
      </button>
    </div>
  </div>
</div>
