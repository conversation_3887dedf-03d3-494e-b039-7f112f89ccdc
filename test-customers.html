<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة العملاء</title>
    <link rel="stylesheet" href="src/css/global.css">
    <link rel="stylesheet" href="src/css/customers.css">
    <link rel="stylesheet" href="src/css/animations.css">
    <style>
        body {
            background: var(--gray-100);
            margin: 0;
            padding: 0;
        }
        .test-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .test-container {
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار صفحة العملاء</h1>
            <p>هذه الصفحة لاختبار جميع مميزات صفحة إدارة العملاء</p>
        </div>
        
        <div id="testContent">
            <!-- سيتم تحميل محتوى صفحة العملاء هنا -->
        </div>
    </div>

    <!-- تحميل قاعدة البيانات المبسطة -->
    <script src="src/js/database-simple.js"></script>
    
    <!-- تحميل المكتبات الخارجية -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    
    <script>
        // تحميل صفحة العملاء
        async function loadCustomersPage() {
            try {
                console.log('🔄 جاري تحميل صفحة العملاء...');
                
                const response = await fetch('src/components/customers.html');
                const html = await response.text();
                
                document.getElementById('testContent').innerHTML = html;
                
                console.log('✅ تم تحميل HTML بنجاح');
                
                // تحميل JavaScript
                const script = document.createElement('script');
                script.src = 'src/js/customers.js';
                document.head.appendChild(script);
                
                script.onload = () => {
                    console.log('✅ تم تحميل JavaScript بنجاح');
                    
                    if (window.CustomersManager) {
                        window.customersManager = new window.CustomersManager();
                        console.log('✅ تم تهيئة مدير العملاء بنجاح');
                        
                        // إضافة بيانات تجريبية
                        addTestData();
                    } else {
                        console.error('❌ فشل في تحميل CustomersManager');
                    }
                };
                
                script.onerror = () => {
                    console.error('❌ فشل في تحميل JavaScript');
                };
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                document.getElementById('testContent').innerHTML = `
                    <div style="padding: 40px; text-align: center; color: var(--danger-color);">
                        <h3>❌ فشل في تحميل صفحة العملاء</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // إضافة بيانات تجريبية
        function addTestData() {
            console.log('📊 إضافة بيانات تجريبية...');
            
            // إضافة عملاء تجريبيين إذا لم يكونوا موجودين
            const existingCustomers = simpleDatabase.getAll('customers');
            if (existingCustomers.length === 0) {
                const testCustomers = [
                    {
                        name: 'أحمد محمد علي السعدي',
                        phone: '0501234567',
                        address: 'الرياض، حي النخيل، شارع الملك فهد',
                        social_media: 'واتساب: 0501234567، تويتر: @ahmed_sa',
                        notes: 'عميل مميز، يفضل التواصل عبر الواتساب',
                        status: 'active',
                        created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'فاطمة أحمد عبدالله',
                        phone: '0507654321',
                        address: 'جدة، حي الصفا، طريق الملك عبدالعزيز',
                        social_media: 'انستقرام: @fatima_a، فيسبوك: Fatima Ahmed',
                        notes: 'عميلة دائمة، تحب الخدمة السريعة',
                        status: 'active',
                        created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'محمد عبدالله الحربي',
                        phone: '0551112233',
                        address: 'الدمام، حي الشاطئ، شارع الخليج',
                        social_media: '',
                        notes: 'يفضل الاتصال المباشر، لا يستخدم وسائل التواصل',
                        status: 'active',
                        created_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'سارة خالد المطيري',
                        phone: '0566778899',
                        address: 'مكة المكرمة، حي العزيزية',
                        social_media: 'واتساب: 0566778899، سناب شات: sara_k',
                        notes: 'عميلة جديدة، تحتاج متابعة خاصة',
                        status: 'active',
                        created_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'عبدالرحمن سعد القحطاني',
                        phone: '0544332211',
                        address: 'المدينة المنورة، حي قباء',
                        social_media: 'تيليجرام: @abdulrahman_s',
                        notes: 'يفضل التواصل عبر التيليجرام، عميل منتظم',
                        status: 'active',
                        created_at: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'نورا عبدالعزيز الدوسري',
                        phone: '0533445566',
                        address: 'الطائف، حي الشفا',
                        social_media: 'واتساب: 0533445566',
                        notes: 'عميلة مهمة، تملك محل للهواتف',
                        status: 'active',
                        created_at: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'يوسف أحمد الغامدي',
                        phone: '0522334455',
                        address: 'أبها، حي المنهل',
                        social_media: '',
                        notes: 'عميل قديم، لكن غير نشط حالياً',
                        status: 'inactive',
                        created_at: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        name: 'ريم محمد الشهري',
                        phone: '0511223344',
                        address: 'خميس مشيط، حي الراقي',
                        social_media: 'انستقرام: @reem_m، تيك توك: @reem_tech',
                        notes: 'عميلة شابة، تحب التقنيات الحديثة',
                        status: 'active',
                        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
                    }
                ];
                
                testCustomers.forEach(customer => {
                    simpleDatabase.insert('customers', customer);
                });
            }
            
            // إضافة طلبات تجريبية لإنشاء ديون
            const existingOrders = simpleDatabase.getAll('repair_orders');
            if (existingOrders.length < 15) {
                const testOrders = [
                    // طلبات للعميل الأول (أحمد) - عليه ديون
                    {
                        order_number: '000001',
                        customer_id: 1,
                        phone_type: 'iPhone 13 Pro',
                        manufacturer_id: 2,
                        problem_description: 'الشاشة مكسورة',
                        price: 500,
                        payment_method: 'partial',
                        paid_amount: 200,
                        remaining_amount: 300,
                        status: 'completed',
                        created_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000002',
                        customer_id: 1,
                        phone_type: 'iPhone 12',
                        manufacturer_id: 2,
                        problem_description: 'مشكلة في البطارية',
                        price: 180,
                        payment_method: 'deferred',
                        paid_amount: 0,
                        remaining_amount: 180,
                        status: 'delivered',
                        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    
                    // طلبات للعميل الثاني (فاطمة) - مسددة
                    {
                        order_number: '000003',
                        customer_id: 2,
                        phone_type: 'Samsung Galaxy S21',
                        manufacturer_id: 1,
                        problem_description: 'لا يعمل',
                        price: 300,
                        payment_method: 'cash',
                        paid_amount: 300,
                        remaining_amount: 0,
                        status: 'delivered',
                        created_at: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    
                    // طلبات للعميل الثالث (محمد) - عليه ديون
                    {
                        order_number: '000004',
                        customer_id: 3,
                        phone_type: 'Huawei P50',
                        manufacturer_id: 3,
                        problem_description: 'مشكلة في الشحن',
                        price: 150,
                        payment_method: 'partial',
                        paid_amount: 50,
                        remaining_amount: 100,
                        status: 'in-progress',
                        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    
                    // طلبات للعميل الرابع (سارة) - مسددة
                    {
                        order_number: '000005',
                        customer_id: 4,
                        phone_type: 'Xiaomi Mi 12',
                        manufacturer_id: 4,
                        problem_description: 'مشكلة في الكاميرا',
                        price: 200,
                        payment_method: 'cash',
                        paid_amount: 200,
                        remaining_amount: 0,
                        status: 'completed',
                        created_at: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    
                    // طلبات للعميل الخامس (عبدالرحمن) - عليه ديون
                    {
                        order_number: '000006',
                        customer_id: 5,
                        phone_type: 'iPhone 11',
                        manufacturer_id: 2,
                        problem_description: 'مشكلة في الصوت',
                        price: 120,
                        payment_method: 'deferred',
                        paid_amount: 0,
                        remaining_amount: 120,
                        status: 'waiting-parts',
                        created_at: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    
                    // طلبات للعميل السادس (نورا) - مختلطة
                    {
                        order_number: '000007',
                        customer_id: 6,
                        phone_type: 'Samsung Galaxy Note 20',
                        manufacturer_id: 1,
                        problem_description: 'تلف في منفذ الشحن',
                        price: 250,
                        payment_method: 'cash',
                        paid_amount: 250,
                        remaining_amount: 0,
                        status: 'delivered',
                        created_at: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000008',
                        customer_id: 6,
                        phone_type: 'iPhone 14',
                        manufacturer_id: 2,
                        problem_description: 'كسر في الشاشة الخلفية',
                        price: 400,
                        payment_method: 'partial',
                        paid_amount: 150,
                        remaining_amount: 250,
                        status: 'pending',
                        created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
                    }
                ];
                
                testOrders.forEach(order => {
                    simpleDatabase.insert('repair_orders', order);
                });
            }
            
            console.log('✅ تم إضافة البيانات التجريبية بنجاح');
            console.log('📊 إحصائيات النظام:', simpleDatabase.getStats());
            
            // تحديث الصفحة إذا كان المدير محملاً
            if (window.customersManager) {
                setTimeout(() => {
                    window.customersManager.loadCustomers();
                }, 1000);
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 بدء اختبار صفحة العملاء');
            loadCustomersPage();
        });
        
        // إضافة معلومات التصحيح
        window.addEventListener('error', (event) => {
            console.error('❌ خطأ JavaScript:', event.error);
        });
        
        // معلومات النظام
        console.log('🔧 معلومات النظام:');
        console.log('- المتصفح:', navigator.userAgent);
        console.log('- اللغة:', navigator.language);
        console.log('- الوقت:', new Date().toLocaleString('ar-SA'));
    </script>
</body>
</html>
