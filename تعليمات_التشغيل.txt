========================================
    نظام إدارة صيانة الموبايلات
========================================

🚀 كيفية تشغيل النظام:

1. الطريقة الأولى (Windows):
   - انقر مرتين على ملف "start.bat"
   - انتظر حتى يتم تحميل النظام

2. الطريقة الثانية (يدوياً):
   - افتح موجه الأوامر (Command Prompt)
   - انتقل إلى مجلد المشروع
   - اكتب: npm start
   - اضغط Enter

🔐 بيانات تسجيل الدخول:
   اسم المستخدم: abd
   كلمة المرور: ZAin1998

📋 المميزات المتاحة حالياً:

✅ تسجيل الدخول الآمن
✅ لوحة التحكم مع الإحصائيات
✅ البحث الشامل في النظام
✅ إضافة طلبات الصيانة
✅ إدارة العملاء
✅ نظام الإشعارات

🔧 المميزات قيد التطوير:
- عرض وإدارة الطلبات
- إدارة قطع الغيار
- التقارير والمستندات
- الإعدادات المتقدمة
- نظام الطباعة

💾 حفظ البيانات:
- جميع البيانات محفوظة محلياً في المتصفح
- لا تحتاج اتصال بالإنترنت
- البيانات آمنة ومحمية

⚠️ ملاحظات مهمة:
- تأكد من تثبيت Node.js على النظام
- لا تغلق نافذة موجه الأوامر أثناء التشغيل
- يمكن الوصول للنظام من المتصفح على العنوان:
  http://localhost:8080 (إذا كان متاحاً)

🆘 في حالة وجود مشاكل:
1. تأكد من تثبيت Node.js
2. أعد تشغيل النظام
3. تحقق من رسائل الخطأ في موجه الأوامر

📞 للدعم الفني:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من سجل الأخطاء في وحدة التحكم

========================================
        تم تطوير النظام بعناية فائقة
    لخدمة محلات صيانة الموبايلات
========================================
