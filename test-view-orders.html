<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة عرض الطلبات</title>
    <link rel="stylesheet" href="src/css/global.css">
    <link rel="stylesheet" href="src/css/view-orders.css">
    <link rel="stylesheet" href="src/css/animations.css">
    <style>
        body {
            background: var(--gray-100);
            margin: 0;
            padding: 0;
        }
        .test-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .test-container {
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار صفحة عرض الطلبات</h1>
            <p>هذه الصفحة لاختبار جميع مميزات صفحة عرض وإدارة طلبات الصيانة</p>
        </div>
        
        <div id="testContent">
            <!-- سيتم تحميل محتوى صفحة عرض الطلبات هنا -->
        </div>
    </div>

    <!-- تحميل قاعدة البيانات المبسطة -->
    <script src="src/js/database-simple.js"></script>
    
    <!-- تحميل المكتبات الخارجية -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <script>
        // تحميل صفحة عرض الطلبات
        async function loadViewOrdersPage() {
            try {
                console.log('🔄 جاري تحميل صفحة عرض الطلبات...');
                
                const response = await fetch('src/components/view-orders.html');
                const html = await response.text();
                
                document.getElementById('testContent').innerHTML = html;
                
                console.log('✅ تم تحميل HTML بنجاح');
                
                // تحميل JavaScript
                const script = document.createElement('script');
                script.src = 'src/js/view-orders.js';
                document.head.appendChild(script);
                
                script.onload = () => {
                    console.log('✅ تم تحميل JavaScript بنجاح');
                    
                    if (window.ViewOrdersManager) {
                        window.viewOrdersManager = new window.ViewOrdersManager();
                        console.log('✅ تم تهيئة مدير عرض الطلبات بنجاح');
                        
                        // إضافة بيانات تجريبية
                        addTestData();
                    } else {
                        console.error('❌ فشل في تحميل ViewOrdersManager');
                    }
                };
                
                script.onerror = () => {
                    console.error('❌ فشل في تحميل JavaScript');
                };
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                document.getElementById('testContent').innerHTML = `
                    <div style="padding: 40px; text-align: center; color: var(--danger-color);">
                        <h3>❌ فشل في تحميل صفحة عرض الطلبات</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // إضافة بيانات تجريبية
        function addTestData() {
            console.log('📊 إضافة بيانات تجريبية...');
            
            // إضافة عملاء تجريبيين إذا لم يكونوا موجودين
            const existingCustomers = simpleDatabase.getAll('customers');
            if (existingCustomers.length === 0) {
                const testCustomers = [
                    {
                        name: 'أحمد محمد علي',
                        phone: '0501234567',
                        address: 'الرياض، حي النخيل',
                        social_media: 'واتساب: 0501234567',
                        notes: 'عميل مميز'
                    },
                    {
                        name: 'فاطمة أحمد',
                        phone: '0507654321',
                        address: 'جدة، حي الصفا',
                        social_media: 'تويتر: @fatima_a',
                        notes: 'عميلة دائمة'
                    },
                    {
                        name: 'محمد عبدالله',
                        phone: '0551112233',
                        address: 'الدمام، حي الشاطئ',
                        social_media: '',
                        notes: ''
                    },
                    {
                        name: 'سارة خالد',
                        phone: '0566778899',
                        address: 'مكة المكرمة',
                        social_media: 'انستقرام: @sara_k',
                        notes: 'عميلة جديدة'
                    },
                    {
                        name: 'عبدالرحمن سعد',
                        phone: '0544332211',
                        address: 'المدينة المنورة',
                        social_media: '',
                        notes: 'يفضل التواصل عبر الواتساب'
                    }
                ];
                
                testCustomers.forEach(customer => {
                    simpleDatabase.insert('customers', customer);
                });
            }
            
            // إضافة طلبات تجريبية متنوعة
            const existingOrders = simpleDatabase.getAll('repair_orders');
            if (existingOrders.length < 10) {
                const testOrders = [
                    {
                        order_number: '000001',
                        customer_id: 1,
                        phone_type: 'iPhone 13 Pro',
                        manufacturer_id: 2,
                        problem_description: 'الشاشة مكسورة والجهاز لا يستجيب للمس',
                        price: 500,
                        payment_method: 'partial',
                        paid_amount: 200,
                        remaining_amount: 300,
                        status: 'in-progress',
                        priority: 'urgent',
                        created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000002',
                        customer_id: 2,
                        phone_type: 'Samsung Galaxy S21',
                        manufacturer_id: 1,
                        problem_description: 'لا يعمل نهائياً، لا يشحن',
                        price: 300,
                        payment_method: 'cash',
                        paid_amount: 300,
                        remaining_amount: 0,
                        status: 'completed',
                        priority: 'normal',
                        created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000003',
                        customer_id: 3,
                        phone_type: 'Huawei P50',
                        manufacturer_id: 3,
                        problem_description: 'مشكلة في البطارية، تفرغ بسرعة',
                        price: 150,
                        payment_method: 'deferred',
                        paid_amount: 0,
                        remaining_amount: 150,
                        status: 'waiting-parts',
                        priority: 'normal',
                        created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000004',
                        customer_id: 4,
                        phone_type: 'Xiaomi Mi 12',
                        manufacturer_id: 4,
                        problem_description: 'مشكلة في الكاميرا الخلفية',
                        price: 200,
                        payment_method: 'partial',
                        paid_amount: 100,
                        remaining_amount: 100,
                        status: 'pending',
                        priority: 'normal',
                        created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000005',
                        customer_id: 5,
                        phone_type: 'iPhone 12',
                        manufacturer_id: 2,
                        problem_description: 'مشكلة في الصوت، لا يعمل السماعة',
                        price: 180,
                        payment_method: 'cash',
                        paid_amount: 180,
                        remaining_amount: 0,
                        status: 'delivered',
                        priority: 'urgent',
                        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
                    },
                    {
                        order_number: '000006',
                        customer_id: 1,
                        phone_type: 'Samsung Galaxy Note 20',
                        manufacturer_id: 1,
                        problem_description: 'تلف في منفذ الشحن',
                        price: 120,
                        payment_method: 'failed',
                        paid_amount: 0,
                        remaining_amount: 120,
                        status: 'failed',
                        priority: 'normal',
                        failure_reason: 'قطعة الغيار غير متوفرة في السوق',
                        created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
                    }
                ];
                
                testOrders.forEach(order => {
                    simpleDatabase.insert('repair_orders', order);
                });
            }
            
            console.log('✅ تم إضافة البيانات التجريبية بنجاح');
            console.log('📊 إحصائيات النظام:', simpleDatabase.getStats());
            
            // تحديث الصفحة إذا كان المدير محملاً
            if (window.viewOrdersManager) {
                setTimeout(() => {
                    window.viewOrdersManager.loadOrders();
                }, 1000);
            }
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 بدء اختبار صفحة عرض الطلبات');
            loadViewOrdersPage();
        });
        
        // إضافة معلومات التصحيح
        window.addEventListener('error', (event) => {
            console.error('❌ خطأ JavaScript:', event.error);
        });
        
        // معلومات النظام
        console.log('🔧 معلومات النظام:');
        console.log('- المتصفح:', navigator.userAgent);
        console.log('- اللغة:', navigator.language);
        console.log('- الوقت:', new Date().toLocaleString('ar-SA'));
    </script>
</body>
</html>
