const database = require('./database');

class LoginManager {
  constructor() {
    this.initializeEventListeners();
    this.loadRememberedCredentials();
  }

  initializeEventListeners() {
    const loginForm = document.getElementById('loginForm');
    const passwordToggle = document.getElementById('passwordToggle');
    const passwordInput = document.getElementById('password');
    const eyeIcon = document.getElementById('eyeIcon');

    // معالج تسجيل الدخول
    loginForm.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    // معالج إظهار/إخفاء كلمة المرور
    passwordToggle.addEventListener('click', () => {
      const isPassword = passwordInput.type === 'password';
      passwordInput.type = isPassword ? 'text' : 'password';
      eyeIcon.className = isPassword ? 'icon-eye-slash' : 'icon-eye';
    });

    // معالج الضغط على Enter
    document.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.handleLogin();
      }
    });
  }

  async handleLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    const loginBtn = document.getElementById('loginBtn');
    const errorMessage = document.getElementById('errorMessage');

    // التحقق من صحة البيانات
    if (!username || !password) {
      this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    // تعطيل الزر أثناء المعالجة
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="icon-loading"></i> جاري التحقق...';

    try {
      // التحقق من بيانات المستخدم من قاعدة البيانات
      const storedUsername = await database.getSetting('username');
      const storedPassword = await database.getSetting('password');

      if (username === storedUsername && password === storedPassword) {
        // تسجيل دخول ناجح
        this.hideError();
        
        // حفظ بيانات التذكر إذا تم اختيارها
        if (rememberMe) {
          localStorage.setItem('rememberedUsername', username);
          localStorage.setItem('rememberCredentials', 'true');
        } else {
          localStorage.removeItem('rememberedUsername');
          localStorage.removeItem('rememberCredentials');
        }

        // تسجيل النشاط
        await database.logActivity('login', 'users', 1, null, { username });

        // إظهار رسالة نجاح
        loginBtn.innerHTML = '<i class="icon-success"></i> تم تسجيل الدخول بنجاح';
        loginBtn.style.background = 'linear-gradient(135deg, var(--success-color), #059669)';

        // حفظ حالة تسجيل الدخول
        sessionStorage.setItem('isLoggedIn', 'true');
        sessionStorage.setItem('currentUser', username);

        // الانتقال إلى لوحة التحكم بعد تأخير قصير
        setTimeout(() => {
          window.location.href = 'dashboard.html';
        }, 1500);

      } else {
        // بيانات خاطئة
        this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
        
        // تسجيل محاولة دخول فاشلة
        await database.logActivity('failed_login', 'users', null, null, { 
          attempted_username: username,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      this.showError('حدث خطأ في النظام، يرجى المحاولة مرة أخرى');
    } finally {
      // إعادة تفعيل الزر
      loginBtn.disabled = false;
      loginBtn.innerHTML = '<i class="icon-login"></i> تسجيل الدخول';
      loginBtn.style.background = '';
    }
  }

  showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    
    errorText.textContent = message;
    errorMessage.style.display = 'flex';
    
    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
      this.hideError();
    }, 5000);
  }

  hideError() {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.style.display = 'none';
  }

  loadRememberedCredentials() {
    const rememberCredentials = localStorage.getItem('rememberCredentials');
    const rememberedUsername = localStorage.getItem('rememberedUsername');

    if (rememberCredentials === 'true' && rememberedUsername) {
      document.getElementById('username').value = rememberedUsername;
      document.getElementById('rememberMe').checked = true;
      document.getElementById('password').focus();
    } else {
      document.getElementById('username').focus();
    }
  }

  // دالة للتحقق من حالة تسجيل الدخول
  static isLoggedIn() {
    // يمكن تطوير هذه الدالة لاحقاً لإضافة نظام جلسات أكثر تطوراً
    return sessionStorage.getItem('isLoggedIn') === 'true';
  }

  // دالة لتسجيل الخروج
  static logout() {
    sessionStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    window.location.href = 'login.html';
  }
}

// تهيئة مدير تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  new LoginManager();
});

// إضافة أيقونات CSS للتحميل
const style = document.createElement('style');
style.textContent = `
  .icon-loading:before { 
    content: '⏳'; 
    animation: spin 1s linear infinite; 
  }
  .icon-success:before { 
    content: '✅'; 
  }
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;
document.head.appendChild(style);
