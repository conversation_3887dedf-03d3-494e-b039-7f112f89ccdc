// استخدام قاعدة البيانات المبسطة

class DashboardManager {
  constructor() {
    this.currentPage = 'dashboard';
    this.searchTimeout = null;
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadDashboardData();
    this.checkAuthentication();
  }

  checkAuthentication() {
    // التحقق من تسجيل الدخول
    const isLoggedIn = sessionStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      window.location.href = 'login.html';
      return;
    }
  }

  initializeEventListeners() {
    // الشريط الجانبي
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    sidebarToggle?.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });

    mobileMenuToggle?.addEventListener('click', () => {
      sidebar.classList.toggle('mobile-open');
    });

    // إغلاق القائمة عند النقر خارجها على الهواتف
    document.addEventListener('click', (e) => {
      if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
          sidebar.classList.remove('mobile-open');
        }
      }
    });

    // التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        this.navigateToPage(page);
      });
    });

    // البحث
    const globalSearch = document.getElementById('globalSearch');
    const searchBtn = document.getElementById('searchBtn');
    const searchModal = document.getElementById('searchModal');
    const closeSearchModal = document.getElementById('closeSearchModal');

    globalSearch?.addEventListener('input', (e) => {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.performSearch(e.target.value);
      }, 300);
    });

    searchBtn?.addEventListener('click', () => {
      this.performSearch(globalSearch.value);
    });

    globalSearch?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.performSearch(e.target.value);
      }
    });

    closeSearchModal?.addEventListener('click', () => {
      searchModal.style.display = 'none';
    });

    searchModal?.addEventListener('click', (e) => {
      if (e.target === searchModal) {
        searchModal.style.display = 'none';
      }
    });

    // تسجيل الخروج
    const logoutBtn = document.getElementById('logoutBtn');
    logoutBtn?.addEventListener('click', () => {
      this.logout();
    });
  }

  async loadDashboardData() {
    try {
      // تحميل الإحصائيات
      await this.loadStatistics();
      
      // تحميل الطلبات الحديثة
      await this.loadRecentOrders();
      
      // تحميل الإشعارات
      await this.loadNotifications();
      
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    }
  }

  async loadStatistics() {
    try {
      const stats = simpleDatabase.getStats();

      document.getElementById('totalOrders').textContent = stats.totalOrders;
      document.getElementById('totalCustomers').textContent = stats.totalCustomers;
      document.getElementById('totalParts').textContent = stats.totalParts;
      document.getElementById('totalDebts').textContent = stats.totalDebts.toFixed(2);

    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
    }
  }

  async loadRecentOrders() {
    try {
      const orders = simpleDatabase.getAll('repair_orders');
      const customers = simpleDatabase.getAll('customers');

      // ربط الطلبات بالعملاء وترتيبها حسب التاريخ
      const recentOrders = orders
        .map(order => {
          const customer = customers.find(c => c.id === order.customer_id);
          return {
            ...order,
            customer_name: customer ? customer.name : 'عميل غير معروف'
          };
        })
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5);

      const container = document.getElementById('recentOrders');
      if (!container) return;

      if (recentOrders.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد طلبات حديثة</p>';
        return;
      }

      container.innerHTML = recentOrders.map(order => `
        <div class="order-item" onclick="viewOrder('${order.id}')">
          <div class="order-info">
            <div>
              <div class="order-number">#${order.order_number || order.id}</div>
              <div class="order-customer">${order.customer_name}</div>
              <div class="order-device">${order.phone_type}</div>
            </div>
          </div>
          <div class="order-status status-${order.status || 'pending'}">
            ${this.getStatusText(order.status || 'pending')}
          </div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في تحميل الطلبات الحديثة:', error);
    }
  }

  async loadNotifications() {
    try {
      const notifications = [];
      const orders = simpleDatabase.getAll('repair_orders');
      const parts = simpleDatabase.getAll('spare_parts');

      // التحقق من الطلبات المتأخرة (أكثر من 7 أيام)
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);

      const overdueOrders = orders.filter(order =>
        order.status === 'pending' && new Date(order.created_at) < weekAgo
      );

      if (overdueOrders.length > 0) {
        notifications.push({
          type: 'warning',
          title: 'طلبات متأخرة',
          text: `يوجد ${overdueOrders.length} طلب متأخر أكثر من أسبوع`
        });
      }

      // التحقق من قطع الغيار المنخفضة
      const lowStockParts = parts.filter(part => (part.quantity || 0) < 5);

      if (lowStockParts.length > 0) {
        notifications.push({
          type: 'warning',
          title: 'مخزون منخفض',
          text: `يوجد ${lowStockParts.length} قطعة غيار بمخزون منخفض`
        });
      }

      // التحقق من الديون المستحقة
      const highDebts = orders.filter(order => (order.remaining_amount || 0) > 100);

      if (highDebts.length > 0) {
        notifications.push({
          type: 'info',
          title: 'ديون مستحقة',
          text: `يوجد ${highDebts.length} عميل بديون تزيد عن 100`
        });
      }

      const container = document.getElementById('notificationsList');
      if (!container) return;

      if (notifications.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد إشعارات جديدة</p>';
        return;
      }

      container.innerHTML = notifications.map(notification => `
        <div class="notification-item notification-${notification.type}">
          <div class="notification-icon">
            <i class="icon-${notification.type === 'warning' ? 'warning' : notification.type === 'info' ? 'info' : 'bell'}"></i>
          </div>
          <div class="notification-content">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-text">${notification.text}</div>
          </div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
    }
  }

  async performSearch(query) {
    if (!query || query.trim().length < 2) {
      return;
    }

    const searchModal = document.getElementById('searchModal');
    const searchResults = document.getElementById('searchResults');

    try {
      searchModal.style.display = 'flex';
      searchResults.innerHTML = '<div class="text-center">جاري البحث...</div>';

      const results = await this.searchInDatabase(query.trim());
      
      if (results.length === 0) {
        searchResults.innerHTML = '<div class="text-center text-muted">لم يتم العثور على نتائج</div>';
        return;
      }

      searchResults.innerHTML = results.map(result => `
        <div class="search-result-item" onclick="openSearchResult('${result.type}', '${result.id}')">
          <div class="result-type">${result.type}</div>
          <div class="result-title">${result.title}</div>
          <div class="result-description">${result.description}</div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في البحث:', error);
      searchResults.innerHTML = '<div class="text-center text-danger">حدث خطأ في البحث</div>';
    }
  }

  async searchInDatabase(query) {
    const results = [];

    try {
      // البحث في العملاء
      const customers = simpleDatabase.search('customers', query, ['name', 'phone']).slice(0, 10);
      customers.forEach(customer => {
        results.push({
          type: 'عميل',
          id: customer.id,
          title: customer.name,
          description: customer.phone
        });
      });

      // البحث في الطلبات
      const orders = simpleDatabase.search('repair_orders', query, ['order_number', 'phone_type']).slice(0, 10);
      const customersData = simpleDatabase.getAll('customers');

      orders.forEach(order => {
        const customer = customersData.find(c => c.id === order.customer_id);
        results.push({
          type: 'طلب صيانة',
          id: order.id,
          title: `#${order.order_number || order.id}`,
          description: `${customer ? customer.name : 'عميل غير معروف'} - ${order.phone_type}`
        });
      });

      // البحث في قطع الغيار
      const parts = simpleDatabase.search('spare_parts', query, ['name', 'barcode']).slice(0, 10);
      parts.forEach(part => {
        results.push({
          type: 'قطعة غيار',
          id: part.id,
          title: part.name,
          description: `الكمية: ${part.quantity || 0} - الباركود: ${part.barcode || 'غير محدد'}`
        });
      });

    } catch (error) {
      console.error('خطأ في البحث في قاعدة البيانات:', error);
    }

    return results;
  }

  navigateToPage(page) {
    // إزالة الفئة النشطة من جميع العناصر
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });

    // إضافة الفئة النشطة للعنصر المحدد
    const activeLink = document.querySelector(`[data-page="${page}"]`);
    if (activeLink) {
      activeLink.closest('.nav-item').classList.add('active');
    }

    // تحديث عنوان الصفحة
    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle) {
      pageTitle.textContent = this.getPageTitle(page);
    }

    // تحميل محتوى الصفحة
    this.loadPageContent(page);
    this.currentPage = page;
  }

  getPageTitle(page) {
    const titles = {
      'dashboard': 'لوحة التحكم',
      'add-repair': 'إضافة صيانة',
      'repairs': 'طلبات الصيانة',
      'customers': 'العملاء',
      'spare-parts': 'قطع الغيار',
      'documents': 'المستندات',
      'reports': 'التقارير',
      'activity-log': 'السجل',
      'settings': 'الإعدادات'
    };
    return titles[page] || 'لوحة التحكم';
  }

  async loadPageContent(page) {
    const contentArea = document.getElementById('contentArea');
    if (!contentArea) return;

    try {
      // هنا سيتم تحميل محتوى كل صفحة
      switch (page) {
        case 'dashboard':
          contentArea.innerHTML = document.getElementById('dashboardContent').outerHTML;
          await this.loadDashboardData();
          break;
        case 'add-repair':
          await this.loadAddRepairPage();
          break;
        case 'view-orders':
          await this.loadViewOrdersPage();
          break;
        case 'customers':
          await this.loadCustomersPage();
          break;
        default:
          contentArea.innerHTML = '<div class="text-center">الصفحة قيد التطوير</div>';
      }
    } catch (error) {
      console.error('خطأ في تحميل محتوى الصفحة:', error);
      contentArea.innerHTML = '<div class="text-center text-danger">حدث خطأ في تحميل الصفحة</div>';
    }
  }

  async loadAddRepairPage() {
    try {
      const response = await fetch('../components/add-repair.html');
      const html = await response.text();

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = html;

      // تحميل مكتبات إضافية إذا لم تكن محملة
      await this.loadExternalLibraries();

      // تحميل وتشغيل JavaScript الخاص بصفحة إضافة الصيانة
      if (!window.AddRepairManager) {
        const script = document.createElement('script');
        script.src = '../js/add-repair.js';
        document.head.appendChild(script);

        // تهيئة مدير إضافة الصيانة بعد تحميل الملف
        script.onload = () => {
          if (window.AddRepairManager) {
            new window.AddRepairManager();
          }
        };
      } else {
        // إذا كان محملاً مسبقاً، أنشئ مثيل جديد
        new window.AddRepairManager();
      }

    } catch (error) {
      console.error('خطأ في تحميل صفحة إضافة الصيانة:', error);
      document.getElementById('contentArea').innerHTML =
        '<div class="text-center text-danger">حدث خطأ في تحميل صفحة إضافة الصيانة</div>';
    }
  }

  async loadExternalLibraries() {
    // تحميل مكتبة JsBarcode للباركود
    if (typeof JsBarcode === 'undefined') {
      const barcodeScript = document.createElement('script');
      barcodeScript.src = 'https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js';
      document.head.appendChild(barcodeScript);
    }

    // تحميل مكتبة jsPDF
    if (typeof jsPDF === 'undefined') {
      const pdfScript = document.createElement('script');
      pdfScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
      document.head.appendChild(pdfScript);
    }

    // تحميل مكتبة html2canvas
    if (typeof html2canvas === 'undefined') {
      const canvasScript = document.createElement('script');
      canvasScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
      document.head.appendChild(canvasScript);
    }

    // تحميل مكتبة XLSX للتصدير
    if (typeof XLSX === 'undefined') {
      const xlsxScript = document.createElement('script');
      xlsxScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
      document.head.appendChild(xlsxScript);
    }
  }

  async loadViewOrdersPage() {
    try {
      const response = await fetch('../components/view-orders.html');
      const html = await response.text();

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = html;

      // تحميل CSS الخاص بصفحة عرض الطلبات
      if (!document.getElementById('view-orders-css')) {
        const link = document.createElement('link');
        link.id = 'view-orders-css';
        link.rel = 'stylesheet';
        link.href = '../css/view-orders.css';
        document.head.appendChild(link);
      }

      // تحميل المكتبات الخارجية
      await this.loadExternalLibraries();

      // تحميل وتشغيل JavaScript الخاص بصفحة عرض الطلبات
      if (!window.ViewOrdersManager) {
        const script = document.createElement('script');
        script.src = '../js/view-orders.js';
        document.head.appendChild(script);

        script.onload = () => {
          if (window.ViewOrdersManager) {
            window.viewOrdersManager = new window.ViewOrdersManager();
          }
        };
      } else {
        // إذا كان محملاً مسبقاً، أنشئ مثيل جديد
        window.viewOrdersManager = new window.ViewOrdersManager();
      }

    } catch (error) {
      console.error('خطأ في تحميل صفحة عرض الطلبات:', error);
      document.getElementById('contentArea').innerHTML =
        '<div class="text-center text-danger">حدث خطأ في تحميل صفحة عرض الطلبات</div>';
    }
  }

  async loadCustomersPage() {
    try {
      const response = await fetch('../components/customers.html');
      const html = await response.text();

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = html;

      // تحميل CSS الخاص بصفحة العملاء
      if (!document.getElementById('customers-css')) {
        const link = document.createElement('link');
        link.id = 'customers-css';
        link.rel = 'stylesheet';
        link.href = '../css/customers.css';
        document.head.appendChild(link);
      }

      // تحميل المكتبات الخارجية
      await this.loadExternalLibraries();

      // تحميل وتشغيل JavaScript الخاص بصفحة العملاء
      if (!window.CustomersManager) {
        const script = document.createElement('script');
        script.src = '../js/customers.js';
        document.head.appendChild(script);

        script.onload = () => {
          if (window.CustomersManager) {
            window.customersManager = new window.CustomersManager();
          }
        };
      } else {
        // إذا كان محملاً مسبقاً، أنشئ مثيل جديد
        window.customersManager = new window.CustomersManager();
      }

    } catch (error) {
      console.error('خطأ في تحميل صفحة العملاء:', error);
      document.getElementById('contentArea').innerHTML =
        '<div class="text-center text-danger">حدث خطأ في تحميل صفحة العملاء</div>';
    }
  }

  getStatusText(status) {
    const statusTexts = {
      'pending': 'قيد الانتظار',
      'in-progress': 'قيد الصيانة',
      'completed': 'مكتمل',
      'failed': 'فشل الصيانة',
      'waiting-parts': 'بانتظار قطع الغيار',
      'delivered': 'تم التسليم'
    };
    return statusTexts[status] || status;
  }

  async logout() {
    try {
      simpleDatabase.logActivity('logout', 'users', 1, null, { timestamp: new Date().toISOString() });
      sessionStorage.removeItem('isLoggedIn');
      sessionStorage.removeItem('currentUser');
      window.location.href = 'login.html';
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      window.location.href = 'login.html';
    }
  }
}

// دوال عامة
window.viewOrder = function(orderId) {
  // سيتم تطوير هذه الدالة لاحقاً
  console.log('عرض الطلب:', orderId);
};

window.openSearchResult = function(type, id) {
  // سيتم تطوير هذه الدالة لاحقاً
  console.log('فتح نتيجة البحث:', type, id);
  document.getElementById('searchModal').style.display = 'none';
};

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  new DashboardManager();
});
