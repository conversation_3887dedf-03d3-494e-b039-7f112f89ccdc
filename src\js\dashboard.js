const database = require('./database');

class DashboardManager {
  constructor() {
    this.currentPage = 'dashboard';
    this.searchTimeout = null;
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadDashboardData();
    this.checkAuthentication();
  }

  checkAuthentication() {
    // التحقق من تسجيل الدخول
    const isLoggedIn = sessionStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      window.location.href = 'login.html';
      return;
    }
  }

  initializeEventListeners() {
    // الشريط الجانبي
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    sidebarToggle?.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });

    mobileMenuToggle?.addEventListener('click', () => {
      sidebar.classList.toggle('mobile-open');
    });

    // التنقل
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const page = link.dataset.page;
        this.navigateToPage(page);
      });
    });

    // البحث
    const globalSearch = document.getElementById('globalSearch');
    const searchBtn = document.getElementById('searchBtn');
    const searchModal = document.getElementById('searchModal');
    const closeSearchModal = document.getElementById('closeSearchModal');

    globalSearch?.addEventListener('input', (e) => {
      clearTimeout(this.searchTimeout);
      this.searchTimeout = setTimeout(() => {
        this.performSearch(e.target.value);
      }, 300);
    });

    searchBtn?.addEventListener('click', () => {
      this.performSearch(globalSearch.value);
    });

    globalSearch?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.performSearch(e.target.value);
      }
    });

    closeSearchModal?.addEventListener('click', () => {
      searchModal.style.display = 'none';
    });

    searchModal?.addEventListener('click', (e) => {
      if (e.target === searchModal) {
        searchModal.style.display = 'none';
      }
    });

    // تسجيل الخروج
    const logoutBtn = document.getElementById('logoutBtn');
    logoutBtn?.addEventListener('click', () => {
      this.logout();
    });
  }

  async loadDashboardData() {
    try {
      // تحميل الإحصائيات
      await this.loadStatistics();
      
      // تحميل الطلبات الحديثة
      await this.loadRecentOrders();
      
      // تحميل الإشعارات
      await this.loadNotifications();
      
    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    }
  }

  async loadStatistics() {
    try {
      // إجمالي الطلبات
      const totalOrders = await database.get('SELECT COUNT(*) as count FROM repair_orders');
      document.getElementById('totalOrders').textContent = totalOrders?.count || 0;

      // إجمالي العملاء
      const totalCustomers = await database.get('SELECT COUNT(*) as count FROM customers');
      document.getElementById('totalCustomers').textContent = totalCustomers?.count || 0;

      // إجمالي قطع الغيار
      const totalParts = await database.get('SELECT COUNT(*) as count FROM spare_parts');
      document.getElementById('totalParts').textContent = totalParts?.count || 0;

      // إجمالي الديون
      const totalDebts = await database.get(`
        SELECT SUM(remaining_amount) as total 
        FROM repair_orders 
        WHERE remaining_amount > 0
      `);
      document.getElementById('totalDebts').textContent = totalDebts?.total || 0;

    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
    }
  }

  async loadRecentOrders() {
    try {
      const recentOrders = await database.all(`
        SELECT ro.*, c.name as customer_name 
        FROM repair_orders ro
        LEFT JOIN customers c ON ro.customer_id = c.id
        ORDER BY ro.created_at DESC
        LIMIT 5
      `);

      const container = document.getElementById('recentOrders');
      if (!container) return;

      if (recentOrders.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد طلبات حديثة</p>';
        return;
      }

      container.innerHTML = recentOrders.map(order => `
        <div class="order-item" onclick="viewOrder('${order.id}')">
          <div class="order-info">
            <div>
              <div class="order-number">#${order.order_number}</div>
              <div class="order-customer">${order.customer_name}</div>
              <div class="order-device">${order.phone_type}</div>
            </div>
          </div>
          <div class="order-status status-${order.status}">
            ${this.getStatusText(order.status)}
          </div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في تحميل الطلبات الحديثة:', error);
    }
  }

  async loadNotifications() {
    try {
      const notifications = [];

      // التحقق من الطلبات المتأخرة
      const overdueOrders = await database.all(`
        SELECT COUNT(*) as count 
        FROM repair_orders 
        WHERE status = 'pending' 
        AND created_at < datetime('now', '-7 days')
      `);

      if (overdueOrders[0]?.count > 0) {
        notifications.push({
          type: 'warning',
          title: 'طلبات متأخرة',
          text: `يوجد ${overdueOrders[0].count} طلب متأخر أكثر من أسبوع`
        });
      }

      // التحقق من قطع الغيار المنخفضة
      const lowStockParts = await database.all(`
        SELECT COUNT(*) as count 
        FROM spare_parts 
        WHERE quantity < 5
      `);

      if (lowStockParts[0]?.count > 0) {
        notifications.push({
          type: 'warning',
          title: 'مخزون منخفض',
          text: `يوجد ${lowStockParts[0].count} قطعة غيار بمخزون منخفض`
        });
      }

      // التحقق من الديون المستحقة
      const highDebts = await database.all(`
        SELECT COUNT(*) as count 
        FROM repair_orders 
        WHERE remaining_amount > 100
      `);

      if (highDebts[0]?.count > 0) {
        notifications.push({
          type: 'info',
          title: 'ديون مستحقة',
          text: `يوجد ${highDebts[0].count} عميل بديون تزيد عن 100`
        });
      }

      const container = document.getElementById('notificationsList');
      if (!container) return;

      if (notifications.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد إشعارات جديدة</p>';
        return;
      }

      container.innerHTML = notifications.map(notification => `
        <div class="notification-item notification-${notification.type}">
          <div class="notification-icon">
            <i class="icon-${notification.type === 'warning' ? 'warning' : notification.type === 'info' ? 'info' : 'bell'}"></i>
          </div>
          <div class="notification-content">
            <div class="notification-title">${notification.title}</div>
            <div class="notification-text">${notification.text}</div>
          </div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
    }
  }

  async performSearch(query) {
    if (!query || query.trim().length < 2) {
      return;
    }

    const searchModal = document.getElementById('searchModal');
    const searchResults = document.getElementById('searchResults');

    try {
      searchModal.style.display = 'flex';
      searchResults.innerHTML = '<div class="text-center">جاري البحث...</div>';

      const results = await this.searchInDatabase(query.trim());
      
      if (results.length === 0) {
        searchResults.innerHTML = '<div class="text-center text-muted">لم يتم العثور على نتائج</div>';
        return;
      }

      searchResults.innerHTML = results.map(result => `
        <div class="search-result-item" onclick="openSearchResult('${result.type}', '${result.id}')">
          <div class="result-type">${result.type}</div>
          <div class="result-title">${result.title}</div>
          <div class="result-description">${result.description}</div>
        </div>
      `).join('');

    } catch (error) {
      console.error('خطأ في البحث:', error);
      searchResults.innerHTML = '<div class="text-center text-danger">حدث خطأ في البحث</div>';
    }
  }

  async searchInDatabase(query) {
    const results = [];

    try {
      // البحث في العملاء
      const customers = await database.all(`
        SELECT id, name, phone 
        FROM customers 
        WHERE name LIKE ? OR phone LIKE ?
        LIMIT 10
      `, [`%${query}%`, `%${query}%`]);

      customers.forEach(customer => {
        results.push({
          type: 'عميل',
          id: customer.id,
          title: customer.name,
          description: customer.phone
        });
      });

      // البحث في الطلبات
      const orders = await database.all(`
        SELECT ro.id, ro.order_number, c.name as customer_name, ro.phone_type
        FROM repair_orders ro
        LEFT JOIN customers c ON ro.customer_id = c.id
        WHERE ro.order_number LIKE ? OR c.name LIKE ? OR ro.phone_type LIKE ?
        LIMIT 10
      `, [`%${query}%`, `%${query}%`, `%${query}%`]);

      orders.forEach(order => {
        results.push({
          type: 'طلب صيانة',
          id: order.id,
          title: `#${order.order_number}`,
          description: `${order.customer_name} - ${order.phone_type}`
        });
      });

      // البحث في قطع الغيار
      const parts = await database.all(`
        SELECT id, name, barcode, quantity
        FROM spare_parts 
        WHERE name LIKE ? OR barcode LIKE ?
        LIMIT 10
      `, [`%${query}%`, `%${query}%`]);

      parts.forEach(part => {
        results.push({
          type: 'قطعة غيار',
          id: part.id,
          title: part.name,
          description: `الكمية: ${part.quantity} - الباركود: ${part.barcode || 'غير محدد'}`
        });
      });

    } catch (error) {
      console.error('خطأ في البحث في قاعدة البيانات:', error);
    }

    return results;
  }

  navigateToPage(page) {
    // إزالة الفئة النشطة من جميع العناصر
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });

    // إضافة الفئة النشطة للعنصر المحدد
    const activeLink = document.querySelector(`[data-page="${page}"]`);
    if (activeLink) {
      activeLink.closest('.nav-item').classList.add('active');
    }

    // تحديث عنوان الصفحة
    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle) {
      pageTitle.textContent = this.getPageTitle(page);
    }

    // تحميل محتوى الصفحة
    this.loadPageContent(page);
    this.currentPage = page;
  }

  getPageTitle(page) {
    const titles = {
      'dashboard': 'لوحة التحكم',
      'add-repair': 'إضافة صيانة',
      'repairs': 'طلبات الصيانة',
      'customers': 'العملاء',
      'spare-parts': 'قطع الغيار',
      'documents': 'المستندات',
      'reports': 'التقارير',
      'activity-log': 'السجل',
      'settings': 'الإعدادات'
    };
    return titles[page] || 'لوحة التحكم';
  }

  async loadPageContent(page) {
    const contentArea = document.getElementById('contentArea');
    if (!contentArea) return;

    try {
      // هنا سيتم تحميل محتوى كل صفحة
      switch (page) {
        case 'dashboard':
          contentArea.innerHTML = document.getElementById('dashboardContent').outerHTML;
          await this.loadDashboardData();
          break;
        case 'add-repair':
          await this.loadAddRepairPage();
          break;
        default:
          contentArea.innerHTML = '<div class="text-center">الصفحة قيد التطوير</div>';
      }
    } catch (error) {
      console.error('خطأ في تحميل محتوى الصفحة:', error);
      contentArea.innerHTML = '<div class="text-center text-danger">حدث خطأ في تحميل الصفحة</div>';
    }
  }

  async loadAddRepairPage() {
    try {
      const response = await fetch('../components/add-repair.html');
      const html = await response.text();

      const contentArea = document.getElementById('contentArea');
      contentArea.innerHTML = html;

      // تحميل وتشغيل JavaScript الخاص بصفحة إضافة الصيانة
      const script = document.createElement('script');
      script.src = '../js/add-repair.js';
      document.head.appendChild(script);

      // تهيئة مدير إضافة الصيانة بعد تحميل الملف
      script.onload = () => {
        if (window.AddRepairManager) {
          new window.AddRepairManager();
        }
      };

    } catch (error) {
      console.error('خطأ في تحميل صفحة إضافة الصيانة:', error);
      document.getElementById('contentArea').innerHTML =
        '<div class="text-center text-danger">حدث خطأ في تحميل صفحة إضافة الصيانة</div>';
    }
  }

  getStatusText(status) {
    const statusTexts = {
      'pending': 'قيد الانتظار',
      'in-progress': 'قيد الصيانة',
      'completed': 'مكتمل',
      'failed': 'فشل الصيانة',
      'waiting-parts': 'بانتظار قطع الغيار',
      'delivered': 'تم التسليم'
    };
    return statusTexts[status] || status;
  }

  async logout() {
    try {
      await database.logActivity('logout', 'users', 1, null, { timestamp: new Date().toISOString() });
      sessionStorage.removeItem('isLoggedIn');
      window.location.href = 'login.html';
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      window.location.href = 'login.html';
    }
  }
}

// دوال عامة
window.viewOrder = function(orderId) {
  // سيتم تطوير هذه الدالة لاحقاً
  console.log('عرض الطلب:', orderId);
};

window.openSearchResult = function(type, id) {
  // سيتم تطوير هذه الدالة لاحقاً
  console.log('فتح نتيجة البحث:', type, id);
  document.getElementById('searchModal').style.display = 'none';
};

// تهيئة لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  new DashboardManager();
});
