const database = require('./database');
const moment = require('moment');

class AddRepairManager {
  constructor() {
    this.currentOrderData = null;
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadInitialData();
    this.setDefaultDate();
  }

  initializeEventListeners() {
    // نموذج إضافة الصيانة
    const addRepairForm = document.getElementById('addRepairForm');
    addRepairForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleSubmitRepair();
    });

    // اختيار العميل
    const customerSelect = document.getElementById('customerSelect');
    customerSelect?.addEventListener('change', (e) => {
      this.handleCustomerChange(e.target.value);
    });

    // طريقة الدفع
    const paymentMethod = document.getElementById('paymentMethod');
    paymentMethod?.addEventListener('change', (e) => {
      this.handlePaymentMethodChange(e.target.value);
    });

    // حساب المبلغ المتبقي
    const repairPrice = document.getElementById('repairPrice');
    const paidAmount = document.getElementById('paidAmount');
    
    repairPrice?.addEventListener('input', () => this.calculateRemaining());
    paidAmount?.addEventListener('input', () => this.calculateRemaining());

    // أزرار الإجراءات
    const resetBtn = document.getElementById('resetBtn');
    resetBtn?.addEventListener('click', () => this.resetForm());

    const previewBtn = document.getElementById('previewBtn');
    previewBtn?.addEventListener('click', () => this.showReceiptPreview());

    // نافذة إضافة عميل
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    addCustomerBtn?.addEventListener('click', () => this.showAddCustomerModal());

    const addCustomerForm = document.getElementById('addCustomerForm');
    addCustomerForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleAddCustomer();
    });

    // إغلاق النوافذ المنبثقة
    const closeCustomerModal = document.getElementById('closeCustomerModal');
    const cancelCustomerBtn = document.getElementById('cancelCustomerBtn');
    closeCustomerModal?.addEventListener('click', () => this.hideAddCustomerModal());
    cancelCustomerBtn?.addEventListener('click', () => this.hideAddCustomerModal());

    const closeReceiptModal = document.getElementById('closeReceiptModal');
    closeReceiptModal?.addEventListener('click', () => this.hideReceiptModal());

    // طباعة الإيصال
    const printReceiptBtn = document.getElementById('printReceiptBtn');
    printReceiptBtn?.addEventListener('click', () => this.printReceipt());
  }

  async loadInitialData() {
    try {
      // تحميل العملاء
      await this.loadCustomers();
      
      // تحميل الشركات المصنعة
      await this.loadManufacturers();
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأولية:', error);
      this.showError('حدث خطأ في تحميل البيانات');
    }
  }

  async loadCustomers() {
    try {
      const customers = await database.all('SELECT * FROM customers ORDER BY name');
      const customerSelect = document.getElementById('customerSelect');
      
      if (!customerSelect) return;

      // مسح الخيارات الحالية (عدا الخيار الافتراضي)
      customerSelect.innerHTML = '<option value="">اختر عميل موجود أو أضف جديد</option>';
      
      customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = `${customer.name} - ${customer.phone}`;
        option.dataset.phone = customer.phone;
        customerSelect.appendChild(option);
      });

    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  }

  async loadManufacturers() {
    try {
      const manufacturers = await database.all('SELECT * FROM manufacturers ORDER BY name');
      const manufacturerSelect = document.getElementById('manufacturerSelect');
      
      if (!manufacturerSelect) return;

      manufacturers.forEach(manufacturer => {
        const option = document.createElement('option');
        option.value = manufacturer.id;
        option.textContent = manufacturer.name;
        manufacturerSelect.appendChild(option);
      });

    } catch (error) {
      console.error('خطأ في تحميل الشركات المصنعة:', error);
    }
  }

  setDefaultDate() {
    const repairDate = document.getElementById('repairDate');
    if (repairDate) {
      repairDate.value = moment().format('YYYY-MM-DD');
    }
  }

  handleCustomerChange(customerId) {
    const customerSelect = document.getElementById('customerSelect');
    const customerPhone = document.getElementById('customerPhone');
    
    if (customerId && customerSelect) {
      const selectedOption = customerSelect.querySelector(`option[value="${customerId}"]`);
      if (selectedOption && customerPhone) {
        customerPhone.value = selectedOption.dataset.phone || '';
      }
    } else if (customerPhone) {
      customerPhone.value = '';
    }
  }

  handlePaymentMethodChange(method) {
    const paidAmountGroup = document.getElementById('paidAmountGroup');
    const paidAmount = document.getElementById('paidAmount');
    
    if (method === 'partial') {
      paidAmountGroup.style.display = 'block';
      paidAmount.required = true;
    } else {
      paidAmountGroup.style.display = 'none';
      paidAmount.required = false;
      paidAmount.value = '';
    }
    
    this.calculateRemaining();
  }

  calculateRemaining() {
    const repairPrice = parseFloat(document.getElementById('repairPrice')?.value || 0);
    const paymentMethod = document.getElementById('paymentMethod')?.value;
    const paidAmount = parseFloat(document.getElementById('paidAmount')?.value || 0);
    
    let remaining = 0;
    
    if (paymentMethod === 'cash') {
      remaining = 0;
    } else if (paymentMethod === 'partial') {
      remaining = repairPrice - paidAmount;
    } else if (paymentMethod === 'deferred') {
      remaining = repairPrice;
    }
    
    // يمكن إضافة عرض المبلغ المتبقي في واجهة المستخدم هنا
  }

  async handleSubmitRepair() {
    try {
      const submitBtn = document.getElementById('submitBtn');
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="icon-loading"></i> جاري الحفظ...';

      // جمع بيانات النموذج
      const formData = this.collectFormData();
      
      // التحقق من صحة البيانات
      if (!this.validateFormData(formData)) {
        return;
      }

      // إنشاء رقم الطلب
      const orderNumber = await database.getNextOrderNumber();
      
      // حساب المبلغ المتبقي
      const remaining = this.calculateRemainingAmount(formData);
      
      // إدراج الطلب في قاعدة البيانات
      const orderData = {
        order_number: orderNumber,
        customer_id: formData.customer_id,
        phone_type: formData.phone_type,
        manufacturer_id: formData.manufacturer_id,
        problem_description: formData.problem_description,
        price: formData.price,
        payment_method: formData.payment_method,
        paid_amount: formData.paid_amount || 0,
        remaining_amount: remaining,
        notes: formData.notes,
        status: 'pending'
      };

      const result = await database.run(`
        INSERT INTO repair_orders (
          order_number, customer_id, phone_type, manufacturer_id, 
          problem_description, price, payment_method, paid_amount, 
          remaining_amount, notes, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        orderData.order_number, orderData.customer_id, orderData.phone_type,
        orderData.manufacturer_id, orderData.problem_description, orderData.price,
        orderData.payment_method, orderData.paid_amount, orderData.remaining_amount,
        orderData.notes, orderData.status
      ]);

      // حفظ بيانات الطلب للإيصال
      this.currentOrderData = {
        ...orderData,
        id: result.id,
        created_at: new Date().toISOString()
      };

      // تسجيل النشاط
      await database.logActivity('create', 'repair_orders', result.id, null, orderData);

      // إظهار رسالة نجاح
      this.showSuccess('تم إضافة طلب الصيانة بنجاح');
      
      // إظهار زر معاينة الإيصال
      const previewBtn = document.getElementById('previewBtn');
      if (previewBtn) {
        previewBtn.style.display = 'inline-flex';
      }

      // إعادة تعيين النموذج
      setTimeout(() => {
        this.resetForm();
      }, 2000);

    } catch (error) {
      console.error('خطأ في إضافة طلب الصيانة:', error);
      this.showError('حدث خطأ في حفظ طلب الصيانة');
    } finally {
      const submitBtn = document.getElementById('submitBtn');
      submitBtn.disabled = false;
      submitBtn.innerHTML = '<i class="icon-save"></i> إضافة طلب الصيانة';
    }
  }

  collectFormData() {
    return {
      customer_id: document.getElementById('customerSelect')?.value,
      manufacturer_id: document.getElementById('manufacturerSelect')?.value,
      phone_type: document.getElementById('phoneType')?.value,
      problem_description: document.getElementById('problemDescription')?.value,
      price: parseFloat(document.getElementById('repairPrice')?.value || 0),
      payment_method: document.getElementById('paymentMethod')?.value,
      paid_amount: parseFloat(document.getElementById('paidAmount')?.value || 0),
      notes: document.getElementById('repairNotes')?.value
    };
  }

  validateFormData(data) {
    if (!data.customer_id) {
      this.showError('يرجى اختيار العميل');
      return false;
    }
    
    if (!data.phone_type) {
      this.showError('يرجى إدخال نوع الهاتف');
      return false;
    }
    
    if (!data.problem_description) {
      this.showError('يرجى إدخال وصف المشكلة');
      return false;
    }
    
    if (!data.price || data.price <= 0) {
      this.showError('يرجى إدخال سعر صحيح للصيانة');
      return false;
    }
    
    if (!data.payment_method) {
      this.showError('يرجى اختيار طريقة الدفع');
      return false;
    }
    
    if (data.payment_method === 'partial' && (!data.paid_amount || data.paid_amount <= 0)) {
      this.showError('يرجى إدخال المبلغ المدفوع');
      return false;
    }
    
    if (data.payment_method === 'partial' && data.paid_amount > data.price) {
      this.showError('المبلغ المدفوع لا يمكن أن يكون أكبر من سعر الصيانة');
      return false;
    }
    
    return true;
  }

  calculateRemainingAmount(data) {
    if (data.payment_method === 'cash') {
      return 0;
    } else if (data.payment_method === 'partial') {
      return data.price - data.paid_amount;
    } else if (data.payment_method === 'deferred') {
      return data.price;
    }
    return 0;
  }

  showAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  hideAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    if (modal) {
      modal.style.display = 'none';
    }
    
    // إعادة تعيين النموذج
    const form = document.getElementById('addCustomerForm');
    if (form) {
      form.reset();
    }
  }

  async handleAddCustomer() {
    try {
      const formData = new FormData(document.getElementById('addCustomerForm'));
      const customerData = {
        name: formData.get('name'),
        phone: formData.get('phone'),
        address: formData.get('address'),
        social_media: formData.get('social_media'),
        notes: formData.get('notes')
      };

      // التحقق من صحة البيانات
      if (!customerData.name || !customerData.phone) {
        this.showError('يرجى إدخال الاسم ورقم الهاتف');
        return;
      }

      // إدراج العميل الجديد
      const result = await database.run(`
        INSERT INTO customers (name, phone, address, social_media, notes)
        VALUES (?, ?, ?, ?, ?)
      `, [customerData.name, customerData.phone, customerData.address, 
          customerData.social_media, customerData.notes]);

      // تسجيل النشاط
      await database.logActivity('create', 'customers', result.id, null, customerData);

      // إعادة تحميل قائمة العملاء
      await this.loadCustomers();

      // اختيار العميل الجديد
      const customerSelect = document.getElementById('customerSelect');
      if (customerSelect) {
        customerSelect.value = result.id;
        this.handleCustomerChange(result.id);
      }

      // إخفاء النافذة المنبثقة
      this.hideAddCustomerModal();
      
      this.showSuccess('تم إضافة العميل بنجاح');

    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      this.showError('حدث خطأ في إضافة العميل');
    }
  }

  resetForm() {
    const form = document.getElementById('addRepairForm');
    if (form) {
      form.reset();
    }
    
    // إخفاء حقل المبلغ المدفوع
    const paidAmountGroup = document.getElementById('paidAmountGroup');
    if (paidAmountGroup) {
      paidAmountGroup.style.display = 'none';
    }
    
    // إخفاء زر معاينة الإيصال
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
      previewBtn.style.display = 'none';
    }
    
    // تعيين التاريخ الافتراضي
    this.setDefaultDate();
    
    // مسح بيانات الطلب الحالي
    this.currentOrderData = null;
  }

  showSuccess(message) {
    // يمكن تطوير نظام إشعارات أكثر تطوراً
    alert(message);
  }

  showError(message) {
    // يمكن تطوير نظام إشعارات أكثر تطوراً
    alert(message);
  }

  showReceiptPreview() {
    if (!this.currentOrderData) {
      this.showError('لا توجد بيانات طلب لعرضها');
      return;
    }
    
    // إنشاء معاينة الإيصال
    this.generateReceiptPreview();
    
    // إظهار النافذة المنبثقة
    const modal = document.getElementById('receiptModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  hideReceiptModal() {
    const modal = document.getElementById('receiptModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  async generateReceiptPreview() {
    // سيتم تطوير هذه الدالة لإنشاء معاينة الإيصال
    const receiptPreview = document.getElementById('receiptPreview');
    if (receiptPreview) {
      receiptPreview.innerHTML = '<div class="text-center">معاينة الإيصال قيد التطوير</div>';
    }
  }

  printReceipt() {
    // سيتم تطوير دالة الطباعة
    window.print();
  }
}

// تهيئة مدير إضافة الصيانة
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AddRepairManager;
} else {
  window.AddRepairManager = AddRepairManager;
}
