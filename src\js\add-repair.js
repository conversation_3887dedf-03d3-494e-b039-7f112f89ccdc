class AddRepairManager {
  constructor() {
    this.currentOrderData = null;
    this.phoneModels = {
      'Samsung': ['Galaxy S23', 'Galaxy S22', 'Galaxy S21', 'Galaxy Note 20', 'Galaxy A54', 'Galaxy A34'],
      'Apple': ['iPhone 15', 'iPhone 14', 'iPhone 13', 'iPhone 12', 'iPhone 11', 'iPhone SE'],
      'Huawei': ['P60', 'P50', 'Mate 50', 'Nova 11', 'Y90'],
      'Xiaomi': ['Mi 13', 'Mi 12', 'Redmi Note 12', 'Redmi 12', 'POCO X5'],
      'Oppo': ['Find X6', 'Reno 10', 'A98', 'A78'],
      'Vivo': ['X90', 'V29', 'Y36', 'Y27'],
      'OnePlus': ['11', '10T', '10 Pro', 'Nord 3'],
      'Nokia': ['G60', 'G50', 'C31', 'C21'],
      'Sony': ['Xperia 1 V', 'Xperia 5 V', 'Xperia 10 V'],
      'Realme': ['GT 3', 'C55', '11 Pro']
    };
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadInitialData();
    this.setDefaultDate();
  }

  initializeEventListeners() {
    // نموذج إضافة الصيانة
    const addRepairForm = document.getElementById('addRepairForm');
    addRepairForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleSubmitRepair();
    });

    // اختيار العميل
    const customerSelect = document.getElementById('customerSelect');
    customerSelect?.addEventListener('change', (e) => {
      this.handleCustomerChange(e.target.value);
    });

    // اختيار الشركة المصنعة
    const manufacturerSelect = document.getElementById('manufacturerSelect');
    manufacturerSelect?.addEventListener('change', (e) => {
      this.handleManufacturerChange(e.target.value);
    });

    // طريقة الدفع
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.handlePaymentMethodChange(e.target.value);
      });
    });

    // البحث السريع
    const quickSearch = document.getElementById('quickSearch');
    const quickSearchBtn = document.getElementById('quickSearchBtn');

    quickSearch?.addEventListener('input', (e) => {
      this.handleQuickSearch(e.target.value);
    });

    quickSearchBtn?.addEventListener('click', () => {
      this.handleQuickSearch(quickSearch.value);
    });

    // اقتراحات المشاكل
    const suggestionBtns = document.querySelectorAll('.suggestion-btn');
    suggestionBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const text = e.target.dataset.text;
        const problemField = document.getElementById('problemDescription');
        if (problemField) {
          problemField.value = text;
          problemField.focus();
        }
      });
    });

    // حساب المبلغ المتبقي
    const repairPrice = document.getElementById('repairPrice');
    const paidAmount = document.getElementById('paidAmount');

    repairPrice?.addEventListener('input', () => this.calculateRemaining());
    paidAmount?.addEventListener('input', () => this.calculateRemaining());

    // طباعة الإيصال
    const printBtn = document.getElementById('printBtn');
    printBtn?.addEventListener('click', () => this.printReceipt());

    // حفظ PDF
    const saveReceiptBtn = document.getElementById('saveReceiptBtn');
    saveReceiptBtn?.addEventListener('click', () => this.saveReceiptAsPDF());

    // أزرار الإجراءات
    const resetBtn = document.getElementById('resetBtn');
    resetBtn?.addEventListener('click', () => this.resetForm());

    const previewBtn = document.getElementById('previewBtn');
    previewBtn?.addEventListener('click', () => this.showReceiptPreview());

    // نافذة إضافة عميل
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    addCustomerBtn?.addEventListener('click', () => this.showAddCustomerModal());

    const addCustomerForm = document.getElementById('addCustomerForm');
    addCustomerForm?.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleAddCustomer();
    });

    // إغلاق النوافذ المنبثقة
    const closeCustomerModal = document.getElementById('closeCustomerModal');
    const cancelCustomerBtn = document.getElementById('cancelCustomerBtn');
    closeCustomerModal?.addEventListener('click', () => this.hideAddCustomerModal());
    cancelCustomerBtn?.addEventListener('click', () => this.hideAddCustomerModal());

    const closeReceiptModal = document.getElementById('closeReceiptModal');
    closeReceiptModal?.addEventListener('click', () => this.hideReceiptModal());

    // طباعة الإيصال
    const printReceiptBtn = document.getElementById('printReceiptBtn');
    printReceiptBtn?.addEventListener('click', () => this.printReceipt());
  }

  async loadInitialData() {
    try {
      // تحميل العملاء
      await this.loadCustomers();
      
      // تحميل الشركات المصنعة
      await this.loadManufacturers();
      
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأولية:', error);
      this.showError('حدث خطأ في تحميل البيانات');
    }
  }

  async loadCustomers() {
    try {
      const customers = simpleDatabase.getAll('customers').sort((a, b) => a.name.localeCompare(b.name));
      const customerSelect = document.getElementById('customerSelect');

      if (!customerSelect) return;

      // مسح الخيارات الحالية (عدا الخيار الافتراضي)
      customerSelect.innerHTML = '<option value="">اختر عميل موجود أو أضف جديد</option>';

      customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = `${customer.name} - ${customer.phone}`;
        option.dataset.phone = customer.phone;
        option.dataset.address = customer.address || '';
        customerSelect.appendChild(option);
      });

    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
    }
  }

  async loadManufacturers() {
    try {
      const manufacturers = simpleDatabase.getAll('manufacturers').sort((a, b) => a.name.localeCompare(b.name));
      const manufacturerSelect = document.getElementById('manufacturerSelect');

      if (!manufacturerSelect) return;

      manufacturers.forEach(manufacturer => {
        const option = document.createElement('option');
        option.value = manufacturer.id;
        option.textContent = manufacturer.name;
        manufacturerSelect.appendChild(option);
      });

    } catch (error) {
      console.error('خطأ في تحميل الشركات المصنعة:', error);
    }
  }

  setDefaultDate() {
    const repairDate = document.getElementById('repairDate');
    const expectedDate = document.getElementById('expectedDate');

    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    if (repairDate) {
      repairDate.value = todayStr;
    }

    // تعيين التاريخ المتوقع (بعد 3 أيام من اليوم)
    if (expectedDate) {
      const expectedDateObj = new Date(today);
      expectedDateObj.setDate(expectedDateObj.getDate() + 3);
      expectedDate.value = expectedDateObj.toISOString().split('T')[0];
    }
  }

  handleCustomerChange(customerId) {
    const customerSelect = document.getElementById('customerSelect');
    const customerPhone = document.getElementById('customerPhone');
    const customerAddress = document.getElementById('customerAddress');

    if (customerId && customerSelect) {
      const selectedOption = customerSelect.querySelector(`option[value="${customerId}"]`);
      if (selectedOption) {
        if (customerPhone) customerPhone.value = selectedOption.dataset.phone || '';
        if (customerAddress) customerAddress.value = selectedOption.dataset.address || '';
      }
    } else {
      if (customerPhone) customerPhone.value = '';
      if (customerAddress) customerAddress.value = '';
    }
  }

  handleManufacturerChange(manufacturerId) {
    const phoneModel = document.getElementById('phoneModel');
    const phoneType = document.getElementById('phoneType');

    if (!phoneModel) return;

    // مسح الخيارات الحالية
    phoneModel.innerHTML = '<option value="">اختر الطراز (اختياري)</option>';

    if (manufacturerId) {
      const manufacturerSelect = document.getElementById('manufacturerSelect');
      const selectedOption = manufacturerSelect.querySelector(`option[value="${manufacturerId}"]`);
      const manufacturerName = selectedOption ? selectedOption.textContent : '';

      // إضافة طرازات الشركة
      if (this.phoneModels[manufacturerName]) {
        this.phoneModels[manufacturerName].forEach(model => {
          const option = document.createElement('option');
          option.value = model;
          option.textContent = model;
          phoneModel.appendChild(option);
        });
      }
    }

    // مسح حقل نوع الهاتف عند تغيير الشركة
    if (phoneType) phoneType.value = '';
  }

  handleQuickSearch(query) {
    const searchResults = document.getElementById('quickSearchResults');

    if (!query || query.trim().length < 2) {
      searchResults.style.display = 'none';
      return;
    }

    try {
      const results = this.searchInSystem(query.trim());

      if (results.length === 0) {
        searchResults.innerHTML = '<div class="search-result-item">لم يتم العثور على نتائج</div>';
      } else {
        searchResults.innerHTML = results.map(result => `
          <div class="search-result-item" onclick="openSearchResult('${result.type}', '${result.id}')">
            <div class="result-type">${result.type}</div>
            <div class="result-title">${result.title}</div>
            <div class="result-description">${result.description}</div>
          </div>
        `).join('');
      }

      searchResults.style.display = 'block';
    } catch (error) {
      console.error('خطأ في البحث السريع:', error);
      searchResults.style.display = 'none';
    }
  }

  searchInSystem(query) {
    const results = [];

    try {
      // البحث في العملاء
      const customers = simpleDatabase.search('customers', query, ['name', 'phone']).slice(0, 5);
      customers.forEach(customer => {
        results.push({
          type: 'عميل',
          id: customer.id,
          title: customer.name,
          description: customer.phone
        });
      });

      // البحث في الطلبات
      const orders = simpleDatabase.search('repair_orders', query, ['order_number', 'phone_type']).slice(0, 5);
      orders.forEach(order => {
        results.push({
          type: 'طلب صيانة',
          id: order.id,
          title: `#${order.order_number || order.id}`,
          description: order.phone_type
        });
      });

    } catch (error) {
      console.error('خطأ في البحث:', error);
    }

    return results;
  }

  handlePaymentMethodChange(method) {
    const paidAmountGroup = document.getElementById('paidAmountGroup');
    const paidAmount = document.getElementById('paidAmount');

    if (method === 'partial') {
      paidAmountGroup.style.display = 'block';
      paidAmount.required = true;
      paidAmount.focus();
    } else {
      paidAmountGroup.style.display = 'none';
      paidAmount.required = false;
      paidAmount.value = '';
    }

    this.calculateRemaining();
  }

  calculateRemaining() {
    const repairPrice = parseFloat(document.getElementById('repairPrice')?.value || 0);
    const paymentMethod = document.querySelector('input[name="payment_method"]:checked')?.value;
    const paidAmount = parseFloat(document.getElementById('paidAmount')?.value || 0);
    const remainingAmount = document.getElementById('remainingAmount');

    let remaining = 0;

    if (paymentMethod === 'cash') {
      remaining = 0;
    } else if (paymentMethod === 'partial') {
      remaining = Math.max(0, repairPrice - paidAmount);
    } else if (paymentMethod === 'deferred') {
      remaining = repairPrice;
    }

    if (remainingAmount) {
      remainingAmount.value = remaining.toFixed(2);
    }

    return remaining;
  }

  async handleSubmitRepair() {
    try {
      this.showProgress(0, 'بدء المعالجة...');

      const submitBtn = document.getElementById('submitBtn');
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="loading-spinner"></i> جاري الحفظ...';

      this.showProgress(20, 'جمع البيانات...');

      // جمع بيانات النموذج
      const formData = this.collectFormData();

      // التحقق من صحة البيانات
      if (!this.validateFormData(formData)) {
        this.hideProgress();
        return;
      }

      this.showProgress(40, 'إنشاء رقم الطلب...');

      // إنشاء رقم الطلب
      const orderNumber = simpleDatabase.getNextOrderNumber();

      this.showProgress(60, 'حساب المبلغ المتبقي...');

      // حساب المبلغ المتبقي
      const remaining = this.calculateRemainingAmount(formData);

      this.showProgress(80, 'حفظ الطلب...');

      // إدراج الطلب في قاعدة البيانات
      const orderData = {
        order_number: orderNumber,
        customer_id: formData.customer_id,
        phone_type: formData.phone_type,
        manufacturer_id: formData.manufacturer_id,
        phone_model: formData.phone_model,
        problem_description: formData.problem_description,
        price: formData.price,
        payment_method: formData.payment_method,
        paid_amount: formData.paid_amount || 0,
        remaining_amount: remaining,
        priority: formData.priority || 'normal',
        repair_date: formData.repair_date,
        expected_date: formData.expected_date,
        notes: formData.notes,
        status: 'pending'
      };

      const result = simpleDatabase.insert('repair_orders', orderData);

      // حفظ بيانات الطلب للإيصال
      this.currentOrderData = {
        ...orderData,
        id: result.id,
        created_at: new Date().toISOString()
      };

      this.showProgress(90, 'تسجيل النشاط...');

      // تسجيل النشاط
      simpleDatabase.logActivity('create', 'repair_orders', result.id, null, orderData);

      this.showProgress(100, 'تم بنجاح!');

      // إظهار رسالة نجاح مع صوت
      this.showSuccessWithSound('تم إضافة طلب الصيانة بنجاح!');

      // إظهار أزرار الإيصال
      const previewBtn = document.getElementById('previewBtn');
      const printBtn = document.getElementById('printBtn');

      if (previewBtn) previewBtn.style.display = 'inline-flex';
      if (printBtn) printBtn.style.display = 'inline-flex';

      // إخفاء شريط التقدم بعد ثانيتين
      setTimeout(() => {
        this.hideProgress();
      }, 2000);

    } catch (error) {
      console.error('خطأ في إضافة طلب الصيانة:', error);
      this.showError('حدث خطأ في حفظ طلب الصيانة');
      this.hideProgress();
    } finally {
      const submitBtn = document.getElementById('submitBtn');
      submitBtn.disabled = false;
      submitBtn.innerHTML = '<i class="icon-save"></i> إضافة طلب الصيانة';
    }
  }

  collectFormData() {
    return {
      customer_id: document.getElementById('customerSelect')?.value,
      manufacturer_id: document.getElementById('manufacturerSelect')?.value,
      phone_type: document.getElementById('phoneType')?.value,
      phone_model: document.getElementById('phoneModel')?.value,
      problem_description: document.getElementById('problemDescription')?.value,
      price: parseFloat(document.getElementById('repairPrice')?.value || 0),
      payment_method: document.querySelector('input[name="payment_method"]:checked')?.value,
      paid_amount: parseFloat(document.getElementById('paidAmount')?.value || 0),
      priority: document.getElementById('priority')?.value,
      repair_date: document.getElementById('repairDate')?.value,
      expected_date: document.getElementById('expectedDate')?.value,
      notes: document.getElementById('repairNotes')?.value
    };
  }

  validateFormData(data) {
    if (!data.customer_id) {
      this.showError('يرجى اختيار العميل');
      return false;
    }
    
    if (!data.phone_type) {
      this.showError('يرجى إدخال نوع الهاتف');
      return false;
    }
    
    if (!data.problem_description) {
      this.showError('يرجى إدخال وصف المشكلة');
      return false;
    }
    
    if (!data.price || data.price <= 0) {
      this.showError('يرجى إدخال سعر صحيح للصيانة');
      return false;
    }
    
    if (!data.payment_method) {
      this.showError('يرجى اختيار طريقة الدفع');
      return false;
    }
    
    if (data.payment_method === 'partial' && (!data.paid_amount || data.paid_amount <= 0)) {
      this.showError('يرجى إدخال المبلغ المدفوع');
      return false;
    }
    
    if (data.payment_method === 'partial' && data.paid_amount > data.price) {
      this.showError('المبلغ المدفوع لا يمكن أن يكون أكبر من سعر الصيانة');
      return false;
    }
    
    return true;
  }

  calculateRemainingAmount(data) {
    if (data.payment_method === 'cash') {
      return 0;
    } else if (data.payment_method === 'partial') {
      return data.price - data.paid_amount;
    } else if (data.payment_method === 'deferred') {
      return data.price;
    }
    return 0;
  }

  showAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  hideAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    if (modal) {
      modal.style.display = 'none';
    }
    
    // إعادة تعيين النموذج
    const form = document.getElementById('addCustomerForm');
    if (form) {
      form.reset();
    }
  }

  async handleAddCustomer() {
    try {
      const formData = new FormData(document.getElementById('addCustomerForm'));
      const customerData = {
        name: formData.get('name'),
        phone: formData.get('phone'),
        address: formData.get('address'),
        social_media: formData.get('social_media'),
        notes: formData.get('notes')
      };

      // التحقق من صحة البيانات
      if (!customerData.name || !customerData.phone) {
        this.showError('يرجى إدخال الاسم ورقم الهاتف');
        return;
      }

      // التحقق من عدم تكرار رقم الهاتف
      const existingCustomers = simpleDatabase.search('customers', customerData.phone, ['phone']);
      if (existingCustomers.length > 0) {
        this.showError('رقم الهاتف موجود مسبقاً');
        return;
      }

      // إدراج العميل الجديد
      const result = simpleDatabase.insert('customers', customerData);

      // تسجيل النشاط
      simpleDatabase.logActivity('create', 'customers', result.id, null, customerData);

      // إعادة تحميل قائمة العملاء
      await this.loadCustomers();

      // اختيار العميل الجديد
      const customerSelect = document.getElementById('customerSelect');
      if (customerSelect) {
        customerSelect.value = result.id;
        this.handleCustomerChange(result.id);
      }

      // إخفاء النافذة المنبثقة
      this.hideAddCustomerModal();

      this.showSuccessWithSound('تم إضافة العميل بنجاح');

    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      this.showError('حدث خطأ في إضافة العميل');
    }
  }

  resetForm() {
    const form = document.getElementById('addRepairForm');
    if (form) {
      form.reset();
    }

    // إخفاء حقل المبلغ المدفوع
    const paidAmountGroup = document.getElementById('paidAmountGroup');
    if (paidAmountGroup) {
      paidAmountGroup.style.display = 'none';
    }

    // إخفاء أزرار الإيصال
    const previewBtn = document.getElementById('previewBtn');
    const printBtn = document.getElementById('printBtn');

    if (previewBtn) previewBtn.style.display = 'none';
    if (printBtn) printBtn.style.display = 'none';

    // مسح حقول العميل
    const customerPhone = document.getElementById('customerPhone');
    const customerAddress = document.getElementById('customerAddress');
    const phoneModel = document.getElementById('phoneModel');
    const remainingAmount = document.getElementById('remainingAmount');

    if (customerPhone) customerPhone.value = '';
    if (customerAddress) customerAddress.value = '';
    if (remainingAmount) remainingAmount.value = '';

    // إعادة تعيين قائمة طرازات الهاتف
    if (phoneModel) {
      phoneModel.innerHTML = '<option value="">اختر الطراز (اختياري)</option>';
    }

    // تعيين التاريخ الافتراضي
    this.setDefaultDate();

    // مسح بيانات الطلب الحالي
    this.currentOrderData = null;

    // إخفاء نتائج البحث السريع
    const searchResults = document.getElementById('quickSearchResults');
    if (searchResults) {
      searchResults.style.display = 'none';
    }

    // مسح خانة البحث السريع
    const quickSearch = document.getElementById('quickSearch');
    if (quickSearch) {
      quickSearch.value = '';
    }
  }

  showProgress(percentage, text) {
    const progressContainer = document.getElementById('progressContainer');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');

    if (progressContainer) progressContainer.style.display = 'block';
    if (progressFill) progressFill.style.width = `${percentage}%`;
    if (progressText) progressText.textContent = text;
  }

  hideProgress() {
    const progressContainer = document.getElementById('progressContainer');
    if (progressContainer) progressContainer.style.display = 'none';
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showSuccessWithSound(message) {
    this.showNotification(message, 'success');
    this.playNotificationSound();
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="notification-icon icon-${type === 'success' ? 'check' : type === 'error' ? 'warning' : 'info'}"></i>
        <span class="notification-text">${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="icon-close"></i>
      </button>
    `;

    // إضافة الأنماط إذا لم تكن موجودة
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          padding: 16px;
          display: flex;
          align-items: center;
          gap: 12px;
          z-index: 10000;
          animation: slideInRight 0.3s ease-out;
          max-width: 400px;
        }
        .notification-success { border-left: 4px solid var(--success-color); }
        .notification-error { border-left: 4px solid var(--danger-color); }
        .notification-info { border-left: 4px solid var(--info-color); }
        .notification-content { display: flex; align-items: center; gap: 8px; flex: 1; }
        .notification-icon { font-size: 18px; }
        .notification-success .notification-icon { color: var(--success-color); }
        .notification-error .notification-icon { color: var(--danger-color); }
        .notification-close { background: none; border: none; cursor: pointer; color: var(--gray-500); }
        @keyframes slideInRight { from { transform: translateX(100%); } to { transform: translateX(0); } }
      `;
      document.head.appendChild(style);
    }

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  playNotificationSound() {
    try {
      // إنشاء صوت بسيط باستخدام Web Audio API
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (error) {
      console.log('لا يمكن تشغيل الصوت:', error);
    }
  }

  showReceiptPreview() {
    if (!this.currentOrderData) {
      this.showError('لا توجد بيانات طلب لعرضها');
      return;
    }
    
    // إنشاء معاينة الإيصال
    this.generateReceiptPreview();
    
    // إظهار النافذة المنبثقة
    const modal = document.getElementById('receiptModal');
    if (modal) {
      modal.style.display = 'flex';
    }
  }

  hideReceiptModal() {
    const modal = document.getElementById('receiptModal');
    if (modal) {
      modal.style.display = 'none';
    }
  }

  async generateReceiptPreview() {
    if (!this.currentOrderData) {
      this.showError('لا توجد بيانات طلب لعرضها');
      return;
    }

    try {
      // الحصول على بيانات العميل
      const customer = simpleDatabase.getById('customers', this.currentOrderData.customer_id);
      const manufacturer = simpleDatabase.getById('manufacturers', this.currentOrderData.manufacturer_id);

      // الحصول على إعدادات المركز
      const centerName = simpleDatabase.getSetting('center_name') || 'مركز صيانة الموبايلات';
      const centerAddress = simpleDatabase.getSetting('center_address') || 'العنوان';
      const centerPhone = simpleDatabase.getSetting('center_phone') || 'رقم الهاتف';
      const centerSocial = simpleDatabase.getSetting('center_social') || 'وسائل التواصل الاجتماعي';

      // تحديث بيانات الإيصال
      document.getElementById('receiptCenterName').textContent = centerName;
      document.getElementById('receiptCenterAddress').textContent = centerAddress;
      document.getElementById('receiptCenterPhone').textContent = centerPhone;
      document.getElementById('receiptCenterSocial').textContent = centerSocial;

      document.getElementById('receiptOrderNumber').textContent = `#${this.currentOrderData.order_number}`;
      document.getElementById('receiptDate').textContent = new Date(this.currentOrderData.created_at).toLocaleDateString('ar-SA');

      document.getElementById('receiptCustomerName').textContent = customer ? customer.name : 'غير محدد';
      document.getElementById('receiptCustomerPhone').textContent = customer ? customer.phone : 'غير محدد';

      document.getElementById('receiptPhoneType').textContent = this.currentOrderData.phone_type;
      document.getElementById('receiptManufacturer').textContent = manufacturer ? manufacturer.name : 'غير محدد';
      document.getElementById('receiptProblem').textContent = this.currentOrderData.problem_description;

      document.getElementById('receiptTotalPrice').textContent = `${this.currentOrderData.price.toFixed(2)} ريال`;
      document.getElementById('receiptPaymentMethod').textContent = this.getPaymentMethodText(this.currentOrderData.payment_method);
      document.getElementById('receiptPaidAmount').textContent = `${this.currentOrderData.paid_amount.toFixed(2)} ريال`;
      document.getElementById('receiptRemainingAmount').textContent = `${this.currentOrderData.remaining_amount.toFixed(2)} ريال`;

      // إظهار/إخفاء تفاصيل الدفع
      const paymentDetails = document.getElementById('receiptPaymentDetails');
      if (this.currentOrderData.payment_method === 'cash') {
        paymentDetails.style.display = 'none';
      } else {
        paymentDetails.style.display = 'flex';
      }

      // إظهار/إخفاء الملاحظات
      const notesSection = document.getElementById('receiptNotesSection');
      const notesText = document.getElementById('receiptNotes');
      if (this.currentOrderData.notes && this.currentOrderData.notes.trim()) {
        notesText.textContent = this.currentOrderData.notes;
        notesSection.style.display = 'block';
      } else {
        notesSection.style.display = 'none';
      }

      // إنشاء الباركود
      this.generateBarcode(this.currentOrderData.order_number);

      // تحديث الوقت
      document.getElementById('receiptTimestamp').textContent = new Date().toLocaleString('ar-SA');

    } catch (error) {
      console.error('خطأ في إنشاء معاينة الإيصال:', error);
      this.showError('حدث خطأ في إنشاء معاينة الإيصال');
    }
  }

  getPaymentMethodText(method) {
    const methods = {
      'cash': 'نقداً',
      'partial': 'جزء من المبلغ',
      'deferred': 'آجل بعد الصيانة'
    };
    return methods[method] || method;
  }

  generateBarcode(orderNumber) {
    try {
      const canvas = document.getElementById('receiptBarcode');
      const barcodeText = document.getElementById('receiptBarcodeText');

      if (canvas && typeof JsBarcode !== 'undefined') {
        JsBarcode(canvas, orderNumber, {
          format: "CODE128",
          width: 2,
          height: 50,
          displayValue: false
        });
      }

      if (barcodeText) {
        barcodeText.textContent = `#${orderNumber}`;
      }
    } catch (error) {
      console.error('خطأ في إنشاء الباركود:', error);
      // إنشاء باركود بسيط بدون مكتبة
      const canvas = document.getElementById('receiptBarcode');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(orderNumber, canvas.width/2, canvas.height/2);
      }
    }
  }

  printReceipt() {
    try {
      // إخفاء العناصر غير المرغوب في طباعتها
      const elementsToHide = document.querySelectorAll('.modal-header, .modal-actions, .sidebar, .top-bar');
      elementsToHide.forEach(el => el.style.display = 'none');

      // طباعة الصفحة
      window.print();

      // إعادة إظهار العناصر بعد الطباعة
      setTimeout(() => {
        elementsToHide.forEach(el => el.style.display = '');
      }, 1000);

      this.showSuccess('تم إرسال الإيصال للطباعة');
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      this.showError('حدث خطأ في الطباعة');
    }
  }

  async saveReceiptAsPDF() {
    try {
      if (typeof html2canvas === 'undefined' || typeof jsPDF === 'undefined') {
        this.showError('مكتبات PDF غير متوفرة');
        return;
      }

      this.showProgress(0, 'جاري إنشاء PDF...');

      const receiptElement = document.getElementById('receiptPreview');

      this.showProgress(30, 'التقاط الصورة...');

      const canvas = await html2canvas(receiptElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true
      });

      this.showProgress(60, 'إنشاء PDF...');

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a5');

      const imgWidth = 148; // A5 width in mm
      const pageHeight = 210; // A5 height in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      this.showProgress(90, 'حفظ الملف...');

      const fileName = `إيصال_صيانة_${this.currentOrderData.order_number}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      this.showProgress(100, 'تم الحفظ بنجاح!');

      setTimeout(() => {
        this.hideProgress();
        this.showSuccess('تم حفظ الإيصال كملف PDF');
      }, 1000);

    } catch (error) {
      console.error('خطأ في حفظ PDF:', error);
      this.showError('حدث خطأ في حفظ PDF');
      this.hideProgress();
    }
  }
}

// دوال عامة
window.openSearchResult = function(type, id) {
  const searchResults = document.getElementById('quickSearchResults');
  if (searchResults) {
    searchResults.style.display = 'none';
  }

  if (type === 'عميل') {
    const customerSelect = document.getElementById('customerSelect');
    if (customerSelect) {
      customerSelect.value = id;
      const event = new Event('change');
      customerSelect.dispatchEvent(event);
    }
  } else if (type === 'طلب صيانة') {
    // يمكن إضافة منطق لعرض تفاصيل الطلب
    console.log('عرض طلب الصيانة:', id);
  }
};

// تهيئة مدير إضافة الصيانة
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AddRepairManager;
} else {
  window.AddRepairManager = AddRepairManager;
}
