<div class="add-repair-container">
  <div class="page-header">
    <h2 class="page-title">إضافة طلب صيانة جديد</h2>
    <p class="page-subtitle">املأ جميع البيانات المطلوبة لإنشاء طلب صيانة جديد</p>
  </div>

  <form class="repair-form" id="addRepairForm">
    <div class="form-sections">
      <!-- قسم بيانات العميل -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-user"></i>
            بيانات العميل
          </h3>
        </div>
        
        <div class="row">
          <div class="col-8">
            <div class="form-group">
              <label for="customerSelect" class="form-label">العميل</label>
              <div class="customer-input-group">
                <select id="customerSelect" name="customer_id" class="form-control form-select" required>
                  <option value="">اختر عميل موجود أو أضف جديد</option>
                </select>
                <button type="button" class="btn btn-primary" id="addCustomerBtn">
                  <i class="icon-plus"></i>
                  عميل جديد
                </button>
              </div>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="customerPhone" class="form-label">رقم الهاتف</label>
              <input type="tel" id="customerPhone" name="customer_phone" class="form-control" readonly>
            </div>
          </div>
        </div>
      </div>

      <!-- قسم بيانات الجهاز -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-phone"></i>
            بيانات الجهاز
          </h3>
        </div>
        
        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label for="manufacturerSelect" class="form-label">الشركة المصنعة</label>
              <select id="manufacturerSelect" name="manufacturer_id" class="form-control form-select" required>
                <option value="">اختر الشركة المصنعة</option>
              </select>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label for="phoneType" class="form-label">نوع الهاتف</label>
              <input type="text" id="phoneType" name="phone_type" class="form-control" 
                     placeholder="مثال: Galaxy S21, iPhone 13" required>
            </div>
          </div>
        </div>
      </div>

      <!-- قسم تفاصيل الصيانة -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-tools"></i>
            تفاصيل الصيانة
          </h3>
        </div>
        
        <div class="form-group">
          <label for="problemDescription" class="form-label">وصف المشكلة (العطل)</label>
          <textarea id="problemDescription" name="problem_description" class="form-control" 
                    rows="4" placeholder="اكتب وصف مفصل للمشكلة..." required></textarea>
        </div>
        
        <div class="row">
          <div class="col-4">
            <div class="form-group">
              <label for="repairPrice" class="form-label">سعر الصيانة</label>
              <div class="input-group">
                <input type="number" id="repairPrice" name="price" class="form-control" 
                       min="0" step="0.01" placeholder="0.00" required>
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="paymentMethod" class="form-label">طريقة الدفع</label>
              <select id="paymentMethod" name="payment_method" class="form-control form-select" required>
                <option value="">اختر طريقة الدفع</option>
                <option value="cash">نقداً</option>
                <option value="partial">جزء من المبلغ</option>
                <option value="deferred">آجل بعد الصيانة</option>
              </select>
            </div>
          </div>
          <div class="col-4" id="paidAmountGroup" style="display: none;">
            <div class="form-group">
              <label for="paidAmount" class="form-label">المبلغ المدفوع</label>
              <div class="input-group">
                <input type="number" id="paidAmount" name="paid_amount" class="form-control" 
                       min="0" step="0.01" placeholder="0.00">
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label for="repairDate" class="form-label">تاريخ الاستلام</label>
              <input type="date" id="repairDate" name="repair_date" class="form-control" required>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <label for="expectedDate" class="form-label">التاريخ المتوقع للانتهاء</label>
              <input type="date" id="expectedDate" name="expected_date" class="form-control">
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="repairNotes" class="form-label">ملاحظات إضافية</label>
          <textarea id="repairNotes" name="notes" class="form-control" 
                    rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
        </div>
      </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="form-actions">
      <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
        <i class="icon-save"></i>
        إضافة طلب الصيانة
      </button>
      <button type="button" class="btn btn-secondary btn-lg" id="resetBtn">
        <i class="icon-refresh"></i>
        إعادة تعيين
      </button>
      <button type="button" class="btn btn-info btn-lg" id="previewBtn" style="display: none;">
        <i class="icon-eye"></i>
        معاينة الإيصال
      </button>
    </div>
  </form>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal" id="addCustomerModal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <h3>إضافة عميل جديد</h3>
      <button type="button" class="close-modal" id="closeCustomerModal">
        <i class="icon-close"></i>
      </button>
    </div>
    
    <form class="customer-form" id="addCustomerForm">
      <div class="modal-body">
        <div class="form-group">
          <label for="newCustomerName" class="form-label">الاسم الثلاثي</label>
          <input type="text" id="newCustomerName" name="name" class="form-control" 
                 placeholder="الاسم الأول الأوسط الأخير" required>
        </div>
        
        <div class="form-group">
          <label for="newCustomerPhone" class="form-label">رقم الهاتف</label>
          <input type="tel" id="newCustomerPhone" name="phone" class="form-control" 
                 placeholder="05xxxxxxxx" required>
        </div>
        
        <div class="form-group">
          <label for="newCustomerAddress" class="form-label">العنوان</label>
          <textarea id="newCustomerAddress" name="address" class="form-control" 
                    rows="2" placeholder="العنوان التفصيلي"></textarea>
        </div>
        
        <div class="form-group">
          <label for="newCustomerSocial" class="form-label">وسائل التواصل (اختياري)</label>
          <input type="text" id="newCustomerSocial" name="social_media" class="form-control" 
                 placeholder="واتساب، تويتر، إلخ...">
        </div>
        
        <div class="form-group">
          <label for="newCustomerNotes" class="form-label">ملاحظات (اختياري)</label>
          <textarea id="newCustomerNotes" name="notes" class="form-control" 
                    rows="2" placeholder="أي ملاحظات خاصة بالعميل"></textarea>
        </div>
      </div>
      
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">
          <i class="icon-save"></i>
          حفظ العميل
        </button>
        <button type="button" class="btn btn-secondary" id="cancelCustomerBtn">
          إلغاء
        </button>
      </div>
    </form>
  </div>
</div>

<!-- نافذة معاينة الإيصال -->
<div class="modal receipt-modal" id="receiptModal" style="display: none;">
  <div class="modal-content modal-lg">
    <div class="modal-header">
      <h3>معاينة إيصال الصيانة</h3>
      <div class="modal-actions">
        <button type="button" class="btn btn-success" id="printReceiptBtn">
          <i class="icon-print"></i>
          طباعة
        </button>
        <button type="button" class="close-modal" id="closeReceiptModal">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>
    
    <div class="modal-body">
      <div class="receipt-preview" id="receiptPreview">
        <!-- سيتم إنشاء الإيصال هنا -->
      </div>
    </div>
  </div>
</div>

<style>
/* أنماط خاصة بصفحة إضافة الصيانة */
.add-repair-container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: var(--border-radius-lg);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 10px 0;
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.form-sections {
  display: grid;
  gap: 30px;
}

.form-section {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 25px;
  box-shadow: var(--shadow);
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--gray-200);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.section-title i {
  color: var(--primary-color);
  font-size: 20px;
}

.customer-input-group {
  display: flex;
  gap: 10px;
}

.customer-input-group select {
  flex: 1;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group .form-control {
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-text {
  background: var(--gray-100);
  border: 2px solid var(--gray-300);
  border-right: none;
  padding: 12px 16px;
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  font-weight: 500;
  color: var(--gray-600);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 40px;
  padding: 30px;
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
}

.btn-lg {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
}

/* النوافذ المنبثقة */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

.modal-lg {
  max-width: 900px;
}

.modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--gray-50);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
}

.modal-actions {
  display: flex;
  gap: 10px;
}

.modal-body {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background: var(--gray-50);
}

.close-modal {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--gray-500);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

/* معاينة الإيصال */
.receipt-preview {
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  padding: 20px;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

/* الأيقونات */
.icon-user:before { content: '👤'; }
.icon-phone:before { content: '📱'; }
.icon-tools:before { content: '🔧'; }
.icon-save:before { content: '💾'; }
.icon-refresh:before { content: '🔄'; }
.icon-eye:before { content: '👁'; }
.icon-print:before { content: '🖨'; }
.icon-plus:before { content: '➕'; }
.icon-close:before { content: '✖️'; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .customer-input-group {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
</style>
