<div class="add-repair-container">
  <div class="page-header">
    <div class="header-content">
      <div class="header-icon">
        <i class="icon-tools"></i>
      </div>
      <div class="header-text">
        <h2 class="page-title">إضافة طلب صيانة جديد</h2>
        <p class="page-subtitle">املأ جميع البيانات المطلوبة لإنشاء طلب صيانة جديد وإصدار إيصال تلقائي</p>
      </div>
    </div>

    <!-- خانة البحث السريع -->
    <div class="quick-search-container">
      <div class="search-box">
        <input type="text" id="quickSearch" class="quick-search-input"
               placeholder="البحث السريع برقم الطلب أو اسم العميل...">
        <button type="button" class="search-btn" id="quickSearchBtn">
          <i class="icon-search"></i>
        </button>
      </div>
      <div class="search-results" id="quickSearchResults" style="display: none;"></div>
    </div>
  </div>

  <form class="repair-form" id="addRepairForm">
    <div class="form-sections">
      <!-- قسم بيانات العميل -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-user"></i>
            بيانات العميل
          </h3>
        </div>
        
        <div class="row">
          <div class="col-6">
            <div class="form-group">
              <label for="customerSelect" class="form-label">
                <i class="icon-user"></i>
                اسم العميل *
              </label>
              <div class="customer-input-group">
                <select id="customerSelect" name="customer_id" class="form-control form-select" required>
                  <option value="">اختر عميل موجود أو أضف جديد</option>
                </select>
                <button type="button" class="btn btn-success" id="addCustomerBtn">
                  <i class="icon-plus"></i>
                  عميل جديد
                </button>
              </div>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="customerPhone" class="form-label">
                <i class="icon-phone"></i>
                رقم الهاتف
              </label>
              <input type="tel" id="customerPhone" name="customer_phone" class="form-control" readonly>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="customerAddress" class="form-label">
                <i class="icon-location"></i>
                العنوان
              </label>
              <input type="text" id="customerAddress" name="customer_address" class="form-control" readonly>
            </div>
          </div>
        </div>
      </div>

      <!-- قسم بيانات الجهاز -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-phone"></i>
            بيانات الجهاز
          </h3>
        </div>
        
        <div class="row">
          <div class="col-4">
            <div class="form-group">
              <label for="manufacturerSelect" class="form-label">
                <i class="icon-brand"></i>
                الشركة المصنعة *
              </label>
              <select id="manufacturerSelect" name="manufacturer_id" class="form-control form-select" required>
                <option value="">اختر الشركة المصنعة</option>
              </select>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="phoneModel" class="form-label">
                <i class="icon-device"></i>
                طراز الجهاز
              </label>
              <select id="phoneModel" name="phone_model" class="form-control form-select">
                <option value="">اختر الطراز (اختياري)</option>
              </select>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="phoneType" class="form-label">
                <i class="icon-mobile"></i>
                نوع الهاتف *
              </label>
              <input type="text" id="phoneType" name="phone_type" class="form-control"
                     placeholder="مثال: Galaxy S21, iPhone 13" required>
            </div>
          </div>
        </div>
      </div>

      <!-- قسم تفاصيل الصيانة -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="icon-tools"></i>
            تفاصيل الصيانة
          </h3>
        </div>
        
        <div class="form-group">
          <label for="problemDescription" class="form-label">
            <i class="icon-problem"></i>
            وصف المشكلة / العطل *
          </label>
          <textarea id="problemDescription" name="problem_description" class="form-control"
                    rows="4" placeholder="اكتب وصف مفصل ودقيق للمشكلة... (مثال: لا يعمل، الشاشة مكسورة، بطارية تالفة)" required></textarea>
          <div class="problem-suggestions">
            <span class="suggestion-label">اقتراحات سريعة:</span>
            <button type="button" class="suggestion-btn" data-text="الشاشة مكسورة">الشاشة مكسورة</button>
            <button type="button" class="suggestion-btn" data-text="لا يعمل">لا يعمل</button>
            <button type="button" class="suggestion-btn" data-text="بطارية تالفة">بطارية تالفة</button>
            <button type="button" class="suggestion-btn" data-text="مشكلة في الشحن">مشكلة في الشحن</button>
            <button type="button" class="suggestion-btn" data-text="مشكلة في الصوت">مشكلة في الصوت</button>
          </div>
        </div>
        
        <div class="row">
          <div class="col-3">
            <div class="form-group">
              <label for="repairPrice" class="form-label">
                <i class="icon-money"></i>
                سعر الصيانة *
              </label>
              <div class="input-group">
                <input type="number" id="repairPrice" name="price" class="form-control"
                       min="0" step="0.01" placeholder="0.00" required>
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
          <div class="col-3">
            <div class="form-group">
              <label for="paymentMethod" class="form-label">
                <i class="icon-payment"></i>
                طريقة الدفع *
              </label>
              <div class="payment-options">
                <label class="radio-option">
                  <input type="radio" name="payment_method" value="cash" required>
                  <span class="radio-custom"></span>
                  <span class="radio-text">نقداً</span>
                </label>
                <label class="radio-option">
                  <input type="radio" name="payment_method" value="partial" required>
                  <span class="radio-custom"></span>
                  <span class="radio-text">جزء من المبلغ</span>
                </label>
                <label class="radio-option">
                  <input type="radio" name="payment_method" value="deferred" required>
                  <span class="radio-custom"></span>
                  <span class="radio-text">آجل بعد الصيانة</span>
                </label>
              </div>
            </div>
          </div>
          <div class="col-3" id="paidAmountGroup" style="display: none;">
            <div class="form-group">
              <label for="paidAmount" class="form-label">
                <i class="icon-cash"></i>
                المبلغ المدفوع
              </label>
              <div class="input-group">
                <input type="number" id="paidAmount" name="paid_amount" class="form-control"
                       min="0" step="0.01" placeholder="0.00">
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
          <div class="col-3" id="remainingAmountGroup">
            <div class="form-group">
              <label for="remainingAmount" class="form-label">
                <i class="icon-remaining"></i>
                المبلغ المتبقي
              </label>
              <div class="input-group">
                <input type="number" id="remainingAmount" name="remaining_amount" class="form-control"
                       readonly placeholder="0.00">
                <span class="input-group-text">ريال</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-4">
            <div class="form-group">
              <label for="repairDate" class="form-label">
                <i class="icon-calendar"></i>
                تاريخ الاستلام *
              </label>
              <input type="date" id="repairDate" name="repair_date" class="form-control" required>
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="expectedDate" class="form-label">
                <i class="icon-clock"></i>
                التاريخ المتوقع للانتهاء
              </label>
              <input type="date" id="expectedDate" name="expected_date" class="form-control">
            </div>
          </div>
          <div class="col-4">
            <div class="form-group">
              <label for="priority" class="form-label">
                <i class="icon-priority"></i>
                أولوية الطلب
              </label>
              <select id="priority" name="priority" class="form-control form-select">
                <option value="normal">عادي</option>
                <option value="urgent">عاجل</option>
                <option value="very_urgent">عاجل جداً</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label for="repairNotes" class="form-label">ملاحظات إضافية</label>
          <textarea id="repairNotes" name="notes" class="form-control" 
                    rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
        </div>
      </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="form-actions">
      <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
        <i class="icon-save"></i>
        إضافة طلب الصيانة
      </button>
      <button type="button" class="btn btn-secondary btn-lg" id="resetBtn">
        <i class="icon-refresh"></i>
        مسح الحقول
      </button>
      <button type="button" class="btn btn-info btn-lg" id="previewBtn" style="display: none;">
        <i class="icon-eye"></i>
        معاينة الإيصال
      </button>
      <button type="button" class="btn btn-success btn-lg" id="printBtn" style="display: none;">
        <i class="icon-print"></i>
        طباعة الإيصال
      </button>
    </div>

    <!-- شريط التقدم -->
    <div class="progress-container" id="progressContainer" style="display: none;">
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
      <div class="progress-text" id="progressText">جاري المعالجة...</div>
    </div>
  </form>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal" id="addCustomerModal" style="display: none;">
  <div class="modal-content">
    <div class="modal-header">
      <h3>إضافة عميل جديد</h3>
      <button type="button" class="close-modal" id="closeCustomerModal">
        <i class="icon-close"></i>
      </button>
    </div>
    
    <form class="customer-form" id="addCustomerForm">
      <div class="modal-body">
        <div class="form-group">
          <label for="newCustomerName" class="form-label">الاسم الثلاثي</label>
          <input type="text" id="newCustomerName" name="name" class="form-control" 
                 placeholder="الاسم الأول الأوسط الأخير" required>
        </div>
        
        <div class="form-group">
          <label for="newCustomerPhone" class="form-label">رقم الهاتف</label>
          <input type="tel" id="newCustomerPhone" name="phone" class="form-control" 
                 placeholder="05xxxxxxxx" required>
        </div>
        
        <div class="form-group">
          <label for="newCustomerAddress" class="form-label">العنوان</label>
          <textarea id="newCustomerAddress" name="address" class="form-control" 
                    rows="2" placeholder="العنوان التفصيلي"></textarea>
        </div>
        
        <div class="form-group">
          <label for="newCustomerSocial" class="form-label">وسائل التواصل (اختياري)</label>
          <input type="text" id="newCustomerSocial" name="social_media" class="form-control" 
                 placeholder="واتساب، تويتر، إلخ...">
        </div>
        
        <div class="form-group">
          <label for="newCustomerNotes" class="form-label">ملاحظات (اختياري)</label>
          <textarea id="newCustomerNotes" name="notes" class="form-control" 
                    rows="2" placeholder="أي ملاحظات خاصة بالعميل"></textarea>
        </div>
      </div>
      
      <div class="modal-footer">
        <button type="submit" class="btn btn-primary">
          <i class="icon-save"></i>
          حفظ العميل
        </button>
        <button type="button" class="btn btn-secondary" id="cancelCustomerBtn">
          إلغاء
        </button>
      </div>
    </form>
  </div>
</div>

<!-- نافذة معاينة الإيصال -->
<div class="modal receipt-modal" id="receiptModal" style="display: none;">
  <div class="modal-content modal-lg">
    <div class="modal-header">
      <h3>معاينة إيصال الصيانة</h3>
      <div class="modal-actions">
        <button type="button" class="btn btn-success" id="printReceiptBtn">
          <i class="icon-print"></i>
          طباعة الإيصال
        </button>
        <button type="button" class="btn btn-info" id="saveReceiptBtn">
          <i class="icon-download"></i>
          حفظ PDF
        </button>
        <button type="button" class="close-modal" id="closeReceiptModal">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>

    <div class="modal-body">
      <div class="receipt-container">
        <div class="receipt-preview" id="receiptPreview">
          <!-- سيتم إنشاء الإيصال هنا -->
          <div class="receipt-template">
            <!-- رأس الإيصال -->
            <div class="receipt-header">
              <div class="center-logo">
                <img src="../../assets/icons/logo.svg" alt="شعار المركز" class="receipt-logo">
              </div>
              <div class="center-info">
                <h1 class="center-name" id="receiptCenterName">مركز صيانة الموبايلات</h1>
                <div class="center-details">
                  <p class="center-address" id="receiptCenterAddress">العنوان</p>
                  <p class="center-phone" id="receiptCenterPhone">رقم الهاتف</p>
                  <p class="center-social" id="receiptCenterSocial">وسائل التواصل الاجتماعي</p>
                </div>
              </div>
            </div>

            <!-- عنوان المستند -->
            <div class="receipt-title">
              <h2>إيصال صيانة جهاز</h2>
            </div>

            <!-- معلومات الطلب -->
            <div class="receipt-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="info-label">رقم الطلب:</span>
                  <span class="info-value order-number" id="receiptOrderNumber">#000001</span>
                </div>
                <div class="info-item">
                  <span class="info-label">تاريخ الطلب:</span>
                  <span class="info-value" id="receiptDate">2024/12/20</span>
                </div>
              </div>
            </div>

            <!-- بيانات العميل -->
            <div class="receipt-section">
              <h3 class="section-title">بيانات العميل</h3>
              <div class="section-content">
                <div class="info-row">
                  <div class="info-item">
                    <span class="info-label">اسم العميل:</span>
                    <span class="info-value" id="receiptCustomerName">-</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">رقم الهاتف:</span>
                    <span class="info-value" id="receiptCustomerPhone">-</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- تفاصيل الجهاز -->
            <div class="receipt-section">
              <h3 class="section-title">تفاصيل الجهاز</h3>
              <div class="section-content">
                <div class="info-row">
                  <div class="info-item">
                    <span class="info-label">نوع الجهاز:</span>
                    <span class="info-value" id="receiptPhoneType">-</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">الشركة المصنعة:</span>
                    <span class="info-value" id="receiptManufacturer">-</span>
                  </div>
                </div>
                <div class="info-row">
                  <div class="info-item full-width">
                    <span class="info-label">المشكلة:</span>
                    <span class="info-value" id="receiptProblem">-</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- تفاصيل الدفع -->
            <div class="receipt-section">
              <h3 class="section-title">تفاصيل الدفع</h3>
              <div class="section-content">
                <div class="info-row">
                  <div class="info-item">
                    <span class="info-label">السعر الإجمالي:</span>
                    <span class="info-value price" id="receiptTotalPrice">0.00 ريال</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">طريقة الدفع:</span>
                    <span class="info-value" id="receiptPaymentMethod">-</span>
                  </div>
                </div>
                <div class="info-row" id="receiptPaymentDetails">
                  <div class="info-item">
                    <span class="info-label">المبلغ المدفوع:</span>
                    <span class="info-value price" id="receiptPaidAmount">0.00 ريال</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">المبلغ المتبقي:</span>
                    <span class="info-value price remaining" id="receiptRemainingAmount">0.00 ريال</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- ملاحظات -->
            <div class="receipt-section" id="receiptNotesSection" style="display: none;">
              <h3 class="section-title">ملاحظات</h3>
              <div class="section-content">
                <p class="notes-text" id="receiptNotes">-</p>
              </div>
            </div>

            <!-- الباركود -->
            <div class="receipt-barcode">
              <div class="barcode-container">
                <canvas id="receiptBarcode" width="200" height="50"></canvas>
                <p class="barcode-text" id="receiptBarcodeText">#000001</p>
              </div>
            </div>

            <!-- رسالة الشكر -->
            <div class="receipt-footer">
              <div class="thank-you-message">
                <p>شكراً لزيارتكم مركزنا</p>
                <p>نتشرف بخدمتكم دائماً</p>
                <p>نتمنى لكم تجربة ممتازة</p>
              </div>
              <div class="receipt-timestamp">
                <p>تم إنشاء هذا الإيصال في: <span id="receiptTimestamp"></span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* أنماط خاصة بصفحة إضافة الصيانة */
.add-repair-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 40px;
  padding: 30px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.header-text {
  flex: 1;
  text-align: right;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 10px 0;
}

.page-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

/* خانة البحث السريع */
.quick-search-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.quick-search-input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  transition: all 0.3s ease;
}

.quick-search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.quick-search-input:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.2);
}

.search-box .search-btn {
  position: absolute;
  left: 15px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-box .search-btn:hover {
  color: white;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 5px;
}

.form-sections {
  display: grid;
  gap: 30px;
}

.form-section {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 25px;
  box-shadow: var(--shadow);
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--gray-200);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.section-title i {
  color: var(--primary-color);
  font-size: 20px;
}

/* اقتراحات المشاكل */
.problem-suggestions {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.suggestion-label {
  font-size: 12px;
  color: var(--gray-600);
  font-weight: 500;
}

.suggestion-btn {
  padding: 4px 12px;
  background: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: 15px;
  font-size: 12px;
  color: var(--gray-700);
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* خيارات الدفع */
.payment-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: background 0.3s ease;
}

.radio-option:hover {
  background: var(--gray-50);
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-400);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.radio-option input[type="radio"]:checked + .radio-custom:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
}

.radio-text {
  font-weight: 500;
  color: var(--gray-700);
}

/* شريط التقدم */
.progress-container {
  margin-top: 20px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  width: 0%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: var(--gray-600);
  font-weight: 500;
}

.customer-input-group {
  display: flex;
  gap: 10px;
}

.customer-input-group select {
  flex: 1;
}

.input-group {
  display: flex;
  align-items: center;
}

.input-group .form-control {
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group-text {
  background: var(--gray-100);
  border: 2px solid var(--gray-300);
  border-right: none;
  padding: 12px 16px;
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  font-weight: 500;
  color: var(--gray-600);
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 40px;
  padding: 30px;
  background: var(--gray-50);
  border-radius: var(--border-radius-lg);
}

.btn-lg {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
}

/* النوافذ المنبثقة */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

.modal-lg {
  max-width: 900px;
}

.modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--gray-50);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
}

.modal-actions {
  display: flex;
  gap: 10px;
}

.modal-body {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background: var(--gray-50);
}

.close-modal {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--gray-500);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: var(--gray-200);
  color: var(--gray-700);
}

/* معاينة الإيصال */
.receipt-container {
  background: var(--gray-100);
  padding: 20px;
  border-radius: var(--border-radius);
}

.receipt-preview {
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
  box-shadow: var(--shadow);
  max-width: 210mm; /* A5 width */
  margin: 0 auto;
}

.receipt-template {
  padding: 20mm;
  min-height: 297mm; /* A5 height */
}

/* رأس الإيصال */
.receipt-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--primary-color);
}

.receipt-logo {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
}

.center-name {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 10px 0;
}

.center-details p {
  margin: 5px 0;
  font-size: 14px;
  color: var(--gray-600);
}

/* عنوان المستند */
.receipt-title {
  text-align: center;
  margin-bottom: 25px;
}

.receipt-title h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
  padding: 10px 20px;
  background: var(--gray-100);
  border-radius: var(--border-radius);
  display: inline-block;
}

/* معلومات الطلب */
.receipt-info {
  margin-bottom: 25px;
  padding: 15px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item.full-width {
  width: 100%;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px;
}

.info-label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 14px;
}

.info-value {
  color: var(--gray-800);
  font-size: 14px;
}

.order-number {
  color: #dc2626 !important;
  font-weight: 700;
  font-size: 16px;
}

.price {
  font-weight: 600;
  color: var(--success-color);
}

.remaining {
  color: var(--danger-color) !important;
}

/* أقسام الإيصال */
.receipt-section {
  margin-bottom: 20px;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.section-title {
  background: var(--primary-color);
  color: white;
  padding: 10px 15px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.section-content {
  padding: 15px;
}

.notes-text {
  background: var(--gray-50);
  padding: 10px;
  border-radius: var(--border-radius);
  font-style: italic;
  color: var(--gray-700);
  margin: 0;
}

/* الباركود */
.receipt-barcode {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

.barcode-container {
  display: inline-block;
}

.barcode-text {
  margin-top: 10px;
  font-size: 12px;
  color: var(--gray-600);
  font-weight: 500;
}

/* تذييل الإيصال */
.receipt-footer {
  margin-top: 40px;
  text-align: center;
  border-top: 1px solid var(--gray-300);
  padding-top: 20px;
}

.thank-you-message {
  margin-bottom: 20px;
}

.thank-you-message p {
  margin: 5px 0;
  font-size: 14px;
  color: var(--primary-color);
  font-weight: 500;
}

.receipt-timestamp {
  font-size: 12px;
  color: var(--gray-500);
}

/* طباعة الإيصال */
@media print {
  .receipt-container {
    background: none;
    padding: 0;
  }

  .receipt-preview {
    border: none;
    box-shadow: none;
    margin: 0;
    max-width: none;
  }

  .modal-header,
  .modal-actions {
    display: none;
  }
}

/* الأيقونات */
.icon-user:before { content: '👤'; }
.icon-phone:before { content: '📱'; }
.icon-tools:before { content: '🔧'; }
.icon-save:before { content: '💾'; }
.icon-refresh:before { content: '🔄'; }
.icon-eye:before { content: '👁'; }
.icon-print:before { content: '🖨'; }
.icon-plus:before { content: '➕'; }
.icon-close:before { content: '✖️'; }
.icon-search:before { content: '🔍'; }
.icon-location:before { content: '📍'; }
.icon-brand:before { content: '🏢'; }
.icon-device:before { content: '📱'; }
.icon-mobile:before { content: '📲'; }
.icon-problem:before { content: '⚠️'; }
.icon-money:before { content: '💰'; }
.icon-payment:before { content: '💳'; }
.icon-cash:before { content: '💵'; }
.icon-remaining:before { content: '📊'; }
.icon-calendar:before { content: '📅'; }
.icon-clock:before { content: '⏰'; }
.icon-priority:before { content: '🔥'; }
.icon-download:before { content: '⬇️'; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .customer-input-group {
    flex-direction: column;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
}
</style>
