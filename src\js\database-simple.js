// قاعدة بيانات مبسطة تعمل في المتصفح باستخدام localStorage
class SimpleDatabase {
  constructor() {
    this.init();
  }

  init() {
    // إنشاء البيانات الافتراضية إذا لم تكن موجودة
    if (!localStorage.getItem('repair_system_initialized')) {
      this.createDefaultData();
      localStorage.setItem('repair_system_initialized', 'true');
    }
  }

  createDefaultData() {
    // إعدادات افتراضية
    const defaultSettings = {
      system_name: 'نظام إدارة صيانة الموبايلات',
      center_name: 'مركز صيانة الموبايلات',
      center_address: 'العنوان',
      center_phone: 'رقم الهاتف',
      center_social: 'حسابات التواصل الاجتماعي',
      username: 'abd',
      password: 'ZAin1998',
      print_size: 'A5',
      next_order_number: '1'
    };

    localStorage.setItem('settings', JSON.stringify(defaultSettings));

    // شركات مصنعة افتراضية
    const manufacturers = [
      { id: 1, name: 'Samsung' },
      { id: 2, name: 'Apple' },
      { id: 3, name: 'Huawei' },
      { id: 4, name: 'Xiaomi' },
      { id: 5, name: 'Oppo' },
      { id: 6, name: 'Vivo' },
      { id: 7, name: 'OnePlus' },
      { id: 8, name: 'Nokia' },
      { id: 9, name: 'Sony' },
      { id: 10, name: 'LG' },
      { id: 11, name: 'Motorola' },
      { id: 12, name: 'Realme' }
    ];

    localStorage.setItem('manufacturers', JSON.stringify(manufacturers));

    // جداول فارغة
    localStorage.setItem('customers', JSON.stringify([]));
    localStorage.setItem('repair_orders', JSON.stringify([]));
    localStorage.setItem('spare_parts', JSON.stringify([]));
    localStorage.setItem('payments', JSON.stringify([]));
    localStorage.setItem('documents', JSON.stringify([]));
    localStorage.setItem('activity_log', JSON.stringify([]));
  }

  // دوال مساعدة للإعدادات
  getSetting(key) {
    try {
      const settings = JSON.parse(localStorage.getItem('settings') || '{}');
      return settings[key] || null;
    } catch (error) {
      console.error('خطأ في قراءة الإعدادات:', error);
      return null;
    }
  }

  setSetting(key, value) {
    try {
      const settings = JSON.parse(localStorage.getItem('settings') || '{}');
      settings[key] = value;
      localStorage.setItem('settings', JSON.stringify(settings));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      return false;
    }
  }

  // دوال للجداول العامة
  getAll(tableName) {
    try {
      return JSON.parse(localStorage.getItem(tableName) || '[]');
    } catch (error) {
      console.error(`خطأ في قراءة جدول ${tableName}:`, error);
      return [];
    }
  }

  insert(tableName, data) {
    try {
      const records = this.getAll(tableName);
      const newId = records.length > 0 ? Math.max(...records.map(r => r.id || 0)) + 1 : 1;
      
      const newRecord = {
        ...data,
        id: newId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      records.push(newRecord);
      localStorage.setItem(tableName, JSON.stringify(records));
      
      return { id: newId, changes: 1 };
    } catch (error) {
      console.error(`خطأ في إدراج بيانات في جدول ${tableName}:`, error);
      return { id: null, changes: 0 };
    }
  }

  update(tableName, id, data) {
    try {
      const records = this.getAll(tableName);
      const index = records.findIndex(r => r.id === id);
      
      if (index === -1) {
        return { changes: 0 };
      }

      records[index] = {
        ...records[index],
        ...data,
        updated_at: new Date().toISOString()
      };

      localStorage.setItem(tableName, JSON.stringify(records));
      return { changes: 1 };
    } catch (error) {
      console.error(`خطأ في تحديث بيانات في جدول ${tableName}:`, error);
      return { changes: 0 };
    }
  }

  delete(tableName, id) {
    try {
      const records = this.getAll(tableName);
      const filteredRecords = records.filter(r => r.id !== id);
      
      localStorage.setItem(tableName, JSON.stringify(filteredRecords));
      return { changes: records.length - filteredRecords.length };
    } catch (error) {
      console.error(`خطأ في حذف بيانات من جدول ${tableName}:`, error);
      return { changes: 0 };
    }
  }

  getById(tableName, id) {
    try {
      const records = this.getAll(tableName);
      return records.find(r => r.id === id) || null;
    } catch (error) {
      console.error(`خطأ في البحث في جدول ${tableName}:`, error);
      return null;
    }
  }

  search(tableName, searchTerm, fields = []) {
    try {
      const records = this.getAll(tableName);
      if (!searchTerm) return records;

      return records.filter(record => {
        if (fields.length === 0) {
          // البحث في جميع الحقول
          return Object.values(record).some(value => 
            String(value).toLowerCase().includes(searchTerm.toLowerCase())
          );
        } else {
          // البحث في حقول محددة
          return fields.some(field => 
            record[field] && String(record[field]).toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
      });
    } catch (error) {
      console.error(`خطأ في البحث في جدول ${tableName}:`, error);
      return [];
    }
  }

  // دوال خاصة بالنظام
  getNextOrderNumber() {
    try {
      const current = parseInt(this.getSetting('next_order_number') || '1');
      const next = current + 1;
      this.setSetting('next_order_number', next.toString());
      return current.toString().padStart(6, '0');
    } catch (error) {
      console.error('خطأ في إنشاء رقم الطلب:', error);
      return '000001';
    }
  }

  logActivity(action, tableName, recordId, oldData = null, newData = null) {
    try {
      const logEntry = {
        action,
        table_name: tableName,
        record_id: recordId,
        old_data: oldData ? JSON.stringify(oldData) : null,
        new_data: newData ? JSON.stringify(newData) : null,
        user_name: 'admin',
        created_at: new Date().toISOString()
      };

      return this.insert('activity_log', logEntry);
    } catch (error) {
      console.error('خطأ في تسجيل النشاط:', error);
      return { id: null, changes: 0 };
    }
  }

  // إحصائيات سريعة
  getStats() {
    try {
      const customers = this.getAll('customers');
      const orders = this.getAll('repair_orders');
      const parts = this.getAll('spare_parts');
      
      const totalDebts = orders.reduce((sum, order) => {
        return sum + (parseFloat(order.remaining_amount) || 0);
      }, 0);

      return {
        totalCustomers: customers.length,
        totalOrders: orders.length,
        totalParts: parts.length,
        totalDebts: totalDebts
      };
    } catch (error) {
      console.error('خطأ في حساب الإحصائيات:', error);
      return {
        totalCustomers: 0,
        totalOrders: 0,
        totalParts: 0,
        totalDebts: 0
      };
    }
  }

  // نسخ احتياطي
  exportData() {
    try {
      const data = {
        settings: JSON.parse(localStorage.getItem('settings') || '{}'),
        customers: this.getAll('customers'),
        manufacturers: this.getAll('manufacturers'),
        repair_orders: this.getAll('repair_orders'),
        spare_parts: this.getAll('spare_parts'),
        payments: this.getAll('payments'),
        documents: this.getAll('documents'),
        activity_log: this.getAll('activity_log'),
        exported_at: new Date().toISOString()
      };

      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('خطأ في تصدير البيانات:', error);
      return null;
    }
  }

  importData(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      
      // استعادة البيانات
      Object.keys(data).forEach(key => {
        if (key !== 'exported_at') {
          localStorage.setItem(key, JSON.stringify(data[key]));
        }
      });

      return true;
    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      return false;
    }
  }

  // مسح جميع البيانات
  clearAllData() {
    try {
      const keys = [
        'settings', 'customers', 'manufacturers', 'repair_orders',
        'spare_parts', 'payments', 'documents', 'activity_log',
        'repair_system_initialized'
      ];

      keys.forEach(key => localStorage.removeItem(key));
      this.init(); // إعادة إنشاء البيانات الافتراضية
      
      return true;
    } catch (error) {
      console.error('خطأ في مسح البيانات:', error);
      return false;
    }
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
const simpleDatabase = new SimpleDatabase();

// تصدير للاستخدام في ملفات أخرى
if (typeof window !== 'undefined') {
  window.simpleDatabase = simpleDatabase;
}
