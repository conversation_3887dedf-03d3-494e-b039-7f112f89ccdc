<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة إضافة الصيانة</title>
    <link rel="stylesheet" href="src/css/global.css">
    <link rel="stylesheet" href="src/css/animations.css">
    <style>
        body {
            background: var(--gray-100);
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px;
            border-radius: var(--border-radius-lg);
            text-align: center;
            margin-bottom: 30px;
        }
        .test-content {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار صفحة إضافة الصيانة</h1>
            <p>هذه الصفحة لاختبار جميع مميزات صفحة إضافة طلبات الصيانة</p>
        </div>
        
        <div class="test-content" id="testContent">
            <!-- سيتم تحميل محتوى صفحة إضافة الصيانة هنا -->
        </div>
    </div>

    <!-- تحميل قاعدة البيانات المبسطة -->
    <script src="src/js/database-simple.js"></script>
    
    <!-- تحميل المكتبات الخارجية -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        // تحميل صفحة إضافة الصيانة
        async function loadAddRepairPage() {
            try {
                console.log('🔄 جاري تحميل صفحة إضافة الصيانة...');
                
                const response = await fetch('src/components/add-repair.html');
                const html = await response.text();
                
                document.getElementById('testContent').innerHTML = html;
                
                console.log('✅ تم تحميل HTML بنجاح');
                
                // تحميل JavaScript
                const script = document.createElement('script');
                script.src = 'src/js/add-repair.js';
                document.head.appendChild(script);
                
                script.onload = () => {
                    console.log('✅ تم تحميل JavaScript بنجاح');
                    
                    if (window.AddRepairManager) {
                        new window.AddRepairManager();
                        console.log('✅ تم تهيئة مدير إضافة الصيانة بنجاح');
                        
                        // إضافة بيانات تجريبية
                        addTestData();
                    } else {
                        console.error('❌ فشل في تحميل AddRepairManager');
                    }
                };
                
                script.onerror = () => {
                    console.error('❌ فشل في تحميل JavaScript');
                };
                
            } catch (error) {
                console.error('❌ خطأ في تحميل الصفحة:', error);
                document.getElementById('testContent').innerHTML = `
                    <div style="padding: 40px; text-align: center; color: var(--danger-color);">
                        <h3>❌ فشل في تحميل صفحة إضافة الصيانة</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        // إضافة بيانات تجريبية
        function addTestData() {
            console.log('📊 إضافة بيانات تجريبية...');
            
            // إضافة عملاء تجريبيين
            const testCustomers = [
                {
                    name: 'أحمد محمد علي',
                    phone: '0501234567',
                    address: 'الرياض، حي النخيل',
                    social_media: 'واتساب: 0501234567',
                    notes: 'عميل مميز'
                },
                {
                    name: 'فاطمة أحمد',
                    phone: '0507654321',
                    address: 'جدة، حي الصفا',
                    social_media: 'تويتر: @fatima_a',
                    notes: 'عميلة دائمة'
                },
                {
                    name: 'محمد عبدالله',
                    phone: '0551112233',
                    address: 'الدمام، حي الشاطئ',
                    social_media: '',
                    notes: ''
                }
            ];
            
            testCustomers.forEach(customer => {
                simpleDatabase.insert('customers', customer);
            });
            
            // إضافة طلبات تجريبية
            const testOrders = [
                {
                    order_number: '000001',
                    customer_id: 1,
                    phone_type: 'iPhone 13 Pro',
                    manufacturer_id: 2,
                    problem_description: 'الشاشة مكسورة',
                    price: 500,
                    payment_method: 'partial',
                    paid_amount: 200,
                    remaining_amount: 300,
                    status: 'pending'
                },
                {
                    order_number: '000002',
                    customer_id: 2,
                    phone_type: 'Samsung Galaxy S21',
                    manufacturer_id: 1,
                    problem_description: 'لا يعمل',
                    price: 300,
                    payment_method: 'cash',
                    paid_amount: 300,
                    remaining_amount: 0,
                    status: 'in-progress'
                }
            ];
            
            testOrders.forEach(order => {
                simpleDatabase.insert('repair_orders', order);
            });
            
            console.log('✅ تم إضافة البيانات التجريبية بنجاح');
            console.log('📊 إحصائيات النظام:', simpleDatabase.getStats());
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 بدء اختبار صفحة إضافة الصيانة');
            loadAddRepairPage();
        });
        
        // إضافة معلومات التصحيح
        window.addEventListener('error', (event) => {
            console.error('❌ خطأ JavaScript:', event.error);
        });
        
        // معلومات النظام
        console.log('🔧 معلومات النظام:');
        console.log('- المتصفح:', navigator.userAgent);
        console.log('- اللغة:', navigator.language);
        console.log('- الوقت:', new Date().toLocaleString('ar-SA'));
    </script>
</body>
</html>
