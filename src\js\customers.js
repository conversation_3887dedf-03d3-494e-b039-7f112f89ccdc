class CustomersManager {
  constructor() {
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.sortField = 'id';
    this.sortDirection = 'asc';
    this.filters = {
      search: '',
      status: ''
    };
    this.allCustomers = [];
    this.filteredCustomers = [];
    this.currentEditingCustomer = null;
    this.currentDebtCustomer = null;
    
    this.init();
  }

  async init() {
    this.initializeEventListeners();
    await this.loadCustomers();
    this.updateStatsSummary();
  }

  initializeEventListeners() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const clearSearchBtn = document.getElementById('clearSearchBtn');

    searchInput?.addEventListener('input', (e) => {
      this.filters.search = e.target.value;
      this.applyFilters();
      this.toggleClearSearchBtn();
    });

    searchBtn?.addEventListener('click', () => {
      this.applyFilters();
    });

    clearSearchBtn?.addEventListener('click', () => {
      searchInput.value = '';
      this.filters.search = '';
      this.applyFilters();
      this.toggleClearSearchBtn();
    });

    // الفلاتر
    document.getElementById('statusFilter')?.addEventListener('change', (e) => {
      this.filters.status = e.target.value;
      this.applyFilters();
    });

    // أزرار الإجراءات الرئيسية
    document.getElementById('addCustomerBtn')?.addEventListener('click', () => {
      this.showAddCustomerModal();
    });

    document.getElementById('exportExcelBtn')?.addEventListener('click', () => {
      this.exportToExcel();
    });

    document.getElementById('exportPdfBtn')?.addEventListener('click', () => {
      this.exportToPDF();
    });

    document.getElementById('printCardsBtn')?.addEventListener('click', () => {
      this.printCustomerCards();
    });

    document.getElementById('refreshBtn')?.addEventListener('click', () => {
      this.loadCustomers();
    });

    // التنقل بين الصفحات
    document.getElementById('itemsPerPage')?.addEventListener('change', (e) => {
      this.itemsPerPage = parseInt(e.target.value);
      this.currentPage = 1;
      this.renderTable();
    });

    // ترتيب الجدول
    document.querySelectorAll('.sortable').forEach(header => {
      header.addEventListener('click', () => {
        const field = header.dataset.sort;
        this.handleSort(field);
      });
    });

    // النوافذ المنبثقة
    this.initializeModals();
  }

  initializeModals() {
    // نافذة إضافة عميل
    document.getElementById('closeAddModal')?.addEventListener('click', () => {
      this.hideModal('addCustomerModal');
    });

    document.getElementById('cancelAddBtn')?.addEventListener('click', () => {
      this.hideModal('addCustomerModal');
    });

    document.getElementById('saveCustomerBtn')?.addEventListener('click', () => {
      this.saveNewCustomer();
    });

    // نافذة التفاصيل
    document.getElementById('closeDetailsModal')?.addEventListener('click', () => {
      this.hideModal('customerDetailsModal');
    });

    // نافذة التعديل
    document.getElementById('closeEditModal')?.addEventListener('click', () => {
      this.hideModal('editCustomerModal');
    });

    document.getElementById('cancelEditBtn')?.addEventListener('click', () => {
      this.hideModal('editCustomerModal');
    });

    document.getElementById('updateCustomerBtn')?.addEventListener('click', () => {
      this.updateCustomer();
    });

    // نافذة سجل الطلبات
    document.getElementById('closeOrdersModal')?.addEventListener('click', () => {
      this.hideModal('customerOrdersModal');
    });

    // نافذة تسديد الديون
    document.getElementById('closePayDebtModal')?.addEventListener('click', () => {
      this.hideModal('payDebtModal');
    });

    document.getElementById('cancelPaymentBtn')?.addEventListener('click', () => {
      this.hideModal('payDebtModal');
    });

    document.getElementById('confirmPaymentBtn')?.addEventListener('click', () => {
      this.confirmPayment();
    });
  }

  async loadCustomers() {
    try {
      this.showLoading();
      
      // تحميل العملاء من قاعدة البيانات
      this.allCustomers = simpleDatabase.getAll('customers');
      
      // تحميل الطلبات لحساب الإحصائيات
      const orders = simpleDatabase.getAll('repair_orders');
      
      // حساب إحصائيات كل عميل
      this.allCustomers = this.allCustomers.map(customer => {
        const customerOrders = orders.filter(order => order.customer_id === customer.id);
        const totalDebt = customerOrders.reduce((sum, order) => sum + (order.remaining_amount || 0), 0);
        
        return {
          ...customer,
          orders_count: customerOrders.length,
          total_debt: totalDebt,
          total_paid: customerOrders.reduce((sum, order) => sum + (order.paid_amount || 0), 0),
          status: customer.status || (totalDebt > 0 ? 'with-debt' : 'active')
        };
      });

      this.applyFilters();
      this.hideLoading();
      
    } catch (error) {
      console.error('خطأ في تحميل العملاء:', error);
      this.showError('حدث خطأ في تحميل العملاء');
      this.hideLoading();
    }
  }

  applyFilters() {
    let filtered = [...this.allCustomers];

    // فلتر البحث
    if (this.filters.search) {
      const searchTerm = this.filters.search.toLowerCase();
      filtered = filtered.filter(customer => 
        customer.name?.toLowerCase().includes(searchTerm) ||
        customer.phone?.toLowerCase().includes(searchTerm) ||
        customer.id?.toString().includes(searchTerm)
      );
    }

    // فلتر الحالة
    if (this.filters.status) {
      switch (this.filters.status) {
        case 'active':
          filtered = filtered.filter(customer => customer.status === 'active' && customer.total_debt === 0);
          break;
        case 'inactive':
          filtered = filtered.filter(customer => customer.status === 'inactive');
          break;
        case 'with-debt':
          filtered = filtered.filter(customer => customer.total_debt > 0);
          break;
        case 'no-debt':
          filtered = filtered.filter(customer => customer.total_debt === 0);
          break;
      }
    }

    this.filteredCustomers = filtered;
    this.currentPage = 1;
    this.renderTable();
  }

  handleSort(field) {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }

    this.sortCustomers();
    this.renderTable();
    this.updateSortIcons();
  }

  sortCustomers() {
    this.filteredCustomers.sort((a, b) => {
      let aValue = a[this.sortField];
      let bValue = b[this.sortField];

      // معالجة الأرقام
      if (typeof aValue === 'string' && !isNaN(aValue)) {
        aValue = parseFloat(aValue);
        bValue = parseFloat(bValue);
      }

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  renderTable() {
    const tbody = document.getElementById('customersTableBody');
    const noDataMessage = document.getElementById('noDataMessage');
    
    if (!tbody) return;

    // حساب البيانات للصفحة الحالية
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageCustomers = this.filteredCustomers.slice(startIndex, endIndex);

    if (pageCustomers.length === 0) {
      tbody.innerHTML = '';
      noDataMessage.style.display = 'block';
      return;
    }

    noDataMessage.style.display = 'none';

    tbody.innerHTML = pageCustomers.map(customer => `
      <tr>
        <td>
          <span class="customer-id">#${customer.id}</span>
        </td>
        <td>
          <span class="customer-name">${customer.name}</span>
        </td>
        <td>
          <span class="customer-phone">${customer.phone}</span>
        </td>
        <td>${customer.address || 'غير محدد'}</td>
        <td>
          <span class="orders-count">${customer.orders_count}</span>
        </td>
        <td class="debt-amount ${customer.total_debt > 0 ? 'debt-positive' : 'debt-zero'}">
          ${customer.total_debt.toFixed(2)} ريال
        </td>
        <td>
          <span class="customer-status status-${this.getCustomerStatusClass(customer)}">
            ${this.getCustomerStatusText(customer)}
          </span>
        </td>
        <td class="actions-cell">
          <button class="action-btn action-btn-view" onclick="viewCustomerDetails('${customer.id}')" title="عرض التفاصيل">
            <i class="icon-view"></i>
          </button>
          <button class="action-btn action-btn-edit" onclick="editCustomer('${customer.id}')" title="تعديل">
            <i class="icon-edit"></i>
          </button>
          <button class="action-btn action-btn-orders" onclick="viewCustomerOrders('${customer.id}')" title="سجل الطلبات">
            <i class="icon-orders"></i>
          </button>
          ${customer.total_debt > 0 ? `
            <button class="action-btn action-btn-debt" onclick="payCustomerDebt('${customer.id}')" title="تسديد الديون">
              <i class="icon-money"></i>
            </button>
          ` : ''}
          <button class="action-btn action-btn-delete" onclick="deleteCustomer('${customer.id}')" title="حذف">
            <i class="icon-delete"></i>
          </button>
        </td>
      </tr>
    `).join('');

    this.updatePagination();
  }

  updatePagination() {
    const totalRecords = this.filteredCustomers.length;
    const totalPages = Math.ceil(totalRecords / this.itemsPerPage);
    const startRecord = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endRecord = Math.min(this.currentPage * this.itemsPerPage, totalRecords);

    // تحديث معلومات التنقل
    document.getElementById('showingFrom').textContent = totalRecords > 0 ? startRecord : 0;
    document.getElementById('showingTo').textContent = endRecord;
    document.getElementById('totalRecords').textContent = totalRecords;

    // إنشاء أزرار التنقل
    const paginationButtons = document.getElementById('paginationButtons');
    if (!paginationButtons) return;

    let buttonsHTML = '';

    // زر السابق
    buttonsHTML += `
      <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
              onclick="changePage(${this.currentPage - 1})">
        السابق
      </button>
    `;

    // أزرار الصفحات
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      buttonsHTML += `
        <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                onclick="changePage(${i})">
          ${i}
        </button>
      `;
    }

    // زر التالي
    buttonsHTML += `
      <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
              onclick="changePage(${this.currentPage + 1})">
        التالي
      </button>
    `;

    paginationButtons.innerHTML = buttonsHTML;
  }

  updateStatsSummary() {
    const totalCustomers = this.allCustomers.length;
    const activeCustomers = this.allCustomers.filter(customer => 
      customer.status !== 'inactive' && customer.total_debt === 0
    ).length;
    const debtCustomers = this.allCustomers.filter(customer => 
      customer.total_debt > 0
    ).length;

    document.getElementById('totalCustomersCount').textContent = totalCustomers;
    document.getElementById('activeCustomersCount').textContent = activeCustomers;
    document.getElementById('debtCustomersCount').textContent = debtCustomers;
  }

  updateSortIcons() {
    document.querySelectorAll('.sortable').forEach(header => {
      const icon = header.querySelector('.sort-icon');
      header.classList.remove('sorted');
      
      if (header.dataset.sort === this.sortField) {
        header.classList.add('sorted');
        icon.textContent = this.sortDirection === 'asc' ? '↑' : '↓';
      } else {
        icon.textContent = '↕️';
      }
    });
  }

  toggleClearSearchBtn() {
    const clearBtn = document.getElementById('clearSearchBtn');
    if (clearBtn) {
      clearBtn.style.display = this.filters.search ? 'block' : 'none';
    }
  }

  getCustomerStatusClass(customer) {
    if (customer.status === 'inactive') return 'inactive';
    if (customer.total_debt > 0) return 'with-debt';
    return 'active';
  }

  getCustomerStatusText(customer) {
    if (customer.status === 'inactive') return 'غير نشط';
    if (customer.total_debt > 0) return 'عليه ديون';
    return 'نشط';
  }

  // دوال إضافة عميل جديد
  showAddCustomerModal() {
    // مسح النموذج
    document.getElementById('addCustomerForm').reset();
    this.showModal('addCustomerModal');
  }

  async saveNewCustomer() {
    try {
      const formData = new FormData(document.getElementById('addCustomerForm'));
      const customerData = {
        name: formData.get('customerName') || document.getElementById('customerName').value,
        phone: formData.get('customerPhone') || document.getElementById('customerPhone').value,
        address: formData.get('customerAddress') || document.getElementById('customerAddress').value,
        social_media: formData.get('customerSocialMedia') || document.getElementById('customerSocialMedia').value,
        notes: formData.get('customerNotes') || document.getElementById('customerNotes').value,
        status: 'active',
        created_at: new Date().toISOString()
      };

      // التحقق من صحة البيانات
      if (!customerData.name || !customerData.phone) {
        this.showError('يرجى إدخال الاسم ورقم الهاتف');
        return;
      }

      // التحقق من عدم تكرار رقم الهاتف
      const existingCustomers = simpleDatabase.search('customers', customerData.phone, ['phone']);
      if (existingCustomers.length > 0) {
        this.showError('رقم الهاتف موجود مسبقاً');
        return;
      }

      // حفظ العميل الجديد
      const result = simpleDatabase.insert('customers', customerData);

      if (result.id) {
        // تسجيل النشاط
        simpleDatabase.logActivity('create', 'customers', result.id, null, customerData);

        this.showSuccess('تم إضافة العميل بنجاح');
        this.hideModal('addCustomerModal');
        await this.loadCustomers();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في إضافة العميل');
      }

    } catch (error) {
      console.error('خطأ في إضافة العميل:', error);
      this.showError('حدث خطأ في إضافة العميل');
    }
  }

  // دوال عرض التفاصيل
  async viewCustomerDetails(customerId) {
    try {
      const customer = this.allCustomers.find(c => c.id == customerId);
      if (!customer) {
        this.showError('لم يتم العثور على العميل');
        return;
      }

      // تحميل طلبات العميل
      const orders = simpleDatabase.getAll('repair_orders').filter(order => order.customer_id == customerId);

      const detailsContent = document.getElementById('customerDetailsContent');
      if (!detailsContent) return;

      detailsContent.innerHTML = `
        <div class="detail-section">
          <h4>المعلومات الأساسية</h4>
          <div class="detail-row">
            <span class="detail-label">رقم العميل:</span>
            <span class="detail-value customer-id">#${customer.id}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الاسم الثلاثي:</span>
            <span class="detail-value">${customer.name}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">رقم الهاتف:</span>
            <span class="detail-value customer-phone">${customer.phone}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">العنوان:</span>
            <span class="detail-value">${customer.address || 'غير محدد'}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">وسائل التواصل:</span>
            <span class="detail-value">${customer.social_media || 'غير محدد'}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">الحالة:</span>
            <span class="detail-value">
              <span class="customer-status status-${this.getCustomerStatusClass(customer)}">
                ${this.getCustomerStatusText(customer)}
              </span>
            </span>
          </div>
          <div class="detail-row">
            <span class="detail-label">تاريخ التسجيل:</span>
            <span class="detail-value">${this.formatDate(customer.created_at)}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>الإحصائيات المالية</h4>
          <div class="detail-row">
            <span class="detail-label">عدد الطلبات:</span>
            <span class="detail-value orders-count">${customer.orders_count}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">إجمالي المدفوع:</span>
            <span class="detail-value debt-amount debt-zero">${(customer.total_paid || 0).toFixed(2)} ريال</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">إجمالي الديون:</span>
            <span class="detail-value debt-amount ${customer.total_debt > 0 ? 'debt-positive' : 'debt-zero'}">
              ${customer.total_debt.toFixed(2)} ريال
            </span>
          </div>
        </div>

        ${customer.notes ? `
          <div class="detail-section">
            <h4>ملاحظات</h4>
            <div class="detail-row">
              <span class="detail-value">${customer.notes}</span>
            </div>
          </div>
        ` : ''}

        <div class="detail-section">
          <h4>الطلبات الأخيرة (آخر 5 طلبات)</h4>
          ${orders.length > 0 ? `
            <div class="customer-orders-table">
              <table>
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>التاريخ</th>
                    <th>نوع الهاتف</th>
                    <th>الحالة</th>
                    <th>المبلغ</th>
                    <th>المتبقي</th>
                  </tr>
                </thead>
                <tbody>
                  ${orders.slice(-5).reverse().map(order => `
                    <tr onclick="viewOrderDetails('${order.id}')" style="cursor: pointer;">
                      <td>#${order.order_number || order.id}</td>
                      <td>${this.formatDate(order.created_at)}</td>
                      <td>${order.phone_type}</td>
                      <td>
                        <span class="status-badge status-${order.status || 'pending'}">
                          ${this.getOrderStatusText(order.status || 'pending')}
                        </span>
                      </td>
                      <td>${(order.price || 0).toFixed(2)} ريال</td>
                      <td class="${order.remaining_amount > 0 ? 'debt-positive' : 'debt-zero'}">
                        ${(order.remaining_amount || 0).toFixed(2)} ريال
                      </td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          ` : '<p>لا توجد طلبات لهذا العميل</p>'}
        </div>

        <div class="detail-section">
          <h4>إجراءات سريعة</h4>
          <div style="display: flex; gap: 10px; flex-wrap: wrap;">
            <button class="btn btn-warning" onclick="editCustomer('${customer.id}')">
              <i class="icon-edit"></i>
              تعديل البيانات
            </button>
            <button class="btn btn-primary" onclick="viewCustomerOrders('${customer.id}')">
              <i class="icon-orders"></i>
              عرض جميع الطلبات
            </button>
            ${customer.total_debt > 0 ? `
              <button class="btn btn-success" onclick="payCustomerDebt('${customer.id}')">
                <i class="icon-money"></i>
                تسديد الديون
              </button>
            ` : ''}
            <button class="btn btn-info" onclick="printCustomerCard('${customer.id}')">
              <i class="icon-card"></i>
              طباعة البطاقة
            </button>
          </div>
        </div>
      `;

      this.showModal('customerDetailsModal');

    } catch (error) {
      console.error('خطأ في عرض تفاصيل العميل:', error);
      this.showError('حدث خطأ في عرض تفاصيل العميل');
    }
  }

  // دوال التعديل
  async editCustomer(customerId) {
    try {
      const customer = this.allCustomers.find(c => c.id == customerId);
      if (!customer) {
        this.showError('لم يتم العثور على العميل');
        return;
      }

      this.currentEditingCustomer = customer;

      const editForm = document.getElementById('editCustomerForm');
      if (!editForm) return;

      editForm.innerHTML = `
        <div class="form-group">
          <label for="editCustomerName" class="form-label">الاسم الثلاثي *</label>
          <input type="text" id="editCustomerName" class="form-control" value="${customer.name}" required>
        </div>

        <div class="form-group">
          <label for="editCustomerPhone" class="form-label">رقم الهاتف *</label>
          <input type="tel" id="editCustomerPhone" class="form-control" value="${customer.phone}" required>
        </div>

        <div class="form-group">
          <label for="editCustomerAddress" class="form-label">العنوان</label>
          <textarea id="editCustomerAddress" class="form-control" rows="2">${customer.address || ''}</textarea>
        </div>

        <div class="form-group">
          <label for="editCustomerSocialMedia" class="form-label">وسائل التواصل</label>
          <input type="text" id="editCustomerSocialMedia" class="form-control" value="${customer.social_media || ''}">
        </div>

        <div class="form-group">
          <label for="editCustomerNotes" class="form-label">ملاحظات</label>
          <textarea id="editCustomerNotes" class="form-control" rows="3">${customer.notes || ''}</textarea>
        </div>

        <div class="form-group">
          <label for="editCustomerStatus" class="form-label">حالة العميل</label>
          <select id="editCustomerStatus" class="form-control">
            <option value="active" ${customer.status === 'active' ? 'selected' : ''}>نشط</option>
            <option value="inactive" ${customer.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
          </select>
        </div>
      `;

      this.showModal('editCustomerModal');

    } catch (error) {
      console.error('خطأ في تحميل نموذج التعديل:', error);
      this.showError('حدث خطأ في تحميل نموذج التعديل');
    }
  }

  async updateCustomer() {
    try {
      if (!this.currentEditingCustomer) return;

      const updatedData = {
        name: document.getElementById('editCustomerName').value,
        phone: document.getElementById('editCustomerPhone').value,
        address: document.getElementById('editCustomerAddress').value,
        social_media: document.getElementById('editCustomerSocialMedia').value,
        notes: document.getElementById('editCustomerNotes').value,
        status: document.getElementById('editCustomerStatus').value,
        updated_at: new Date().toISOString()
      };

      // التحقق من صحة البيانات
      if (!updatedData.name || !updatedData.phone) {
        this.showError('يرجى ملء الحقول المطلوبة');
        return;
      }

      // التحقق من عدم تكرار رقم الهاتف (إذا تم تغييره)
      if (updatedData.phone !== this.currentEditingCustomer.phone) {
        const existingCustomers = simpleDatabase.search('customers', updatedData.phone, ['phone']);
        if (existingCustomers.length > 0) {
          this.showError('رقم الهاتف موجود مسبقاً');
          return;
        }
      }

      // حفظ التعديلات
      const result = simpleDatabase.update('customers', this.currentEditingCustomer.id, updatedData);

      if (result.changes > 0) {
        // تسجيل النشاط
        simpleDatabase.logActivity('update', 'customers', this.currentEditingCustomer.id,
          this.currentEditingCustomer, updatedData);

        this.showSuccess('تم تحديث بيانات العميل بنجاح');
        this.hideModal('editCustomerModal');
        await this.loadCustomers();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في تحديث البيانات');
      }

    } catch (error) {
      console.error('خطأ في تحديث العميل:', error);
      this.showError('حدث خطأ في تحديث البيانات');
    }
  }

  // دوال الحذف
  async deleteCustomer(customerId) {
    try {
      const customer = this.allCustomers.find(c => c.id == customerId);
      if (!customer) {
        this.showError('لم يتم العثور على العميل');
        return;
      }

      // التحقق من وجود ديون
      if (customer.total_debt > 0) {
        this.showError('لا يمكن حذف العميل لوجود ديون غير مسددة');
        return;
      }

      const confirmed = confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`);

      if (!confirmed) return;

      // حذف العميل
      const result = simpleDatabase.delete('customers', customerId);

      if (result.changes > 0) {
        // تسجيل النشاط
        simpleDatabase.logActivity('delete', 'customers', customerId, customer, null);

        this.showSuccess('تم حذف العميل بنجاح');
        await this.loadCustomers();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في حذف العميل');
      }

    } catch (error) {
      console.error('خطأ في حذف العميل:', error);
      this.showError('حدث خطأ في حذف العميل');
    }
  }

  // دوال عرض سجل الطلبات
  async viewCustomerOrders(customerId) {
    try {
      const customer = this.allCustomers.find(c => c.id == customerId);
      if (!customer) {
        this.showError('لم يتم العثور على العميل');
        return;
      }

      // تحميل طلبات العميل
      const orders = simpleDatabase.getAll('repair_orders').filter(order => order.customer_id == customerId);

      const ordersContent = document.getElementById('customerOrdersContent');
      if (!ordersContent) return;

      if (orders.length === 0) {
        ordersContent.innerHTML = `
          <div class="no-data">
            <div class="no-data-icon">
              <i class="icon-empty"></i>
            </div>
            <h3>لا توجد طلبات</h3>
            <p>لا توجد طلبات صيانة لهذا العميل</p>
          </div>
        `;
      } else {
        ordersContent.innerHTML = `
          <div class="customer-orders-table">
            <table>
              <thead>
                <tr>
                  <th>رقم الطلب</th>
                  <th>تاريخ الطلب</th>
                  <th>نوع الهاتف</th>
                  <th>المشكلة</th>
                  <th>السعر</th>
                  <th>الحالة</th>
                  <th>طريقة الدفع</th>
                  <th>المدفوع</th>
                  <th>المتبقي</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                ${orders.map(order => `
                  <tr>
                    <td>#${order.order_number || order.id}</td>
                    <td>${this.formatDate(order.created_at)}</td>
                    <td>${order.phone_type}</td>
                    <td>${order.problem_description}</td>
                    <td>${(order.price || 0).toFixed(2)} ريال</td>
                    <td>
                      <span class="status-badge status-${order.status || 'pending'}">
                        ${this.getOrderStatusText(order.status || 'pending')}
                      </span>
                    </td>
                    <td>${this.getPaymentMethodText(order.payment_method)}</td>
                    <td class="debt-amount debt-zero">${(order.paid_amount || 0).toFixed(2)} ريال</td>
                    <td class="debt-amount ${order.remaining_amount > 0 ? 'debt-positive' : 'debt-zero'}">
                      ${(order.remaining_amount || 0).toFixed(2)} ريال
                    </td>
                    <td>
                      <button class="action-btn action-btn-view" onclick="viewOrderDetails('${order.id}')" title="عرض">
                        <i class="icon-view"></i>
                      </button>
                      <button class="action-btn action-btn-print" onclick="printOrderReceipt('${order.id}')" title="طباعة">
                        <i class="icon-print"></i>
                      </button>
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
      }

      // إضافة معالجات أزرار التصدير
      document.getElementById('exportCustomerOrdersExcel')?.addEventListener('click', () => {
        this.exportCustomerOrdersToExcel(customer, orders);
      });

      document.getElementById('exportCustomerOrdersPdf')?.addEventListener('click', () => {
        this.exportCustomerOrdersToPDF(customer, orders);
      });

      document.getElementById('printCustomerOrders')?.addEventListener('click', () => {
        this.printCustomerOrders(customer, orders);
      });

      this.showModal('customerOrdersModal');

    } catch (error) {
      console.error('خطأ في عرض سجل الطلبات:', error);
      this.showError('حدث خطأ في عرض سجل الطلبات');
    }
  }

  // دوال تسديد الديون
  async payCustomerDebt(customerId) {
    try {
      const customer = this.allCustomers.find(c => c.id == customerId);
      if (!customer || customer.total_debt <= 0) {
        this.showError('لا يوجد ديون على هذا العميل');
        return;
      }

      this.currentDebtCustomer = customer;

      // تحميل الطلبات غير المسددة
      const unpaidOrders = simpleDatabase.getAll('repair_orders').filter(order =>
        order.customer_id == customerId && order.remaining_amount > 0
      );

      const debtSummary = document.getElementById('debtSummary');
      if (debtSummary) {
        debtSummary.innerHTML = `
          <h4>ملخص ديون العميل</h4>
          <p><strong>اسم العميل:</strong> ${customer.name}</p>
          <p><strong>رقم الهاتف:</strong> ${customer.phone}</p>
          <p><strong>عدد الطلبات غير المسددة:</strong> ${unpaidOrders.length}</p>
          <div class="debt-total">إجمالي الديون: ${customer.total_debt.toFixed(2)} ريال</div>

          <div style="margin-top: 15px;">
            <h5>تفاصيل الطلبات غير المسددة:</h5>
            <ul style="margin: 10px 0; padding-right: 20px;">
              ${unpaidOrders.map(order => `
                <li>طلب #${order.order_number || order.id}: ${order.remaining_amount.toFixed(2)} ريال</li>
              `).join('')}
            </ul>
          </div>
        `;
      }

      // تعيين القيم الافتراضية
      const paymentAmount = document.getElementById('paymentAmount');
      const paymentDate = document.getElementById('paymentDate');

      if (paymentAmount) {
        paymentAmount.max = customer.total_debt;
        paymentAmount.value = customer.total_debt;
      }

      if (paymentDate) {
        paymentDate.value = new Date().toISOString().slice(0, 16);
      }

      this.showModal('payDebtModal');

    } catch (error) {
      console.error('خطأ في فتح نافذة تسديد الديون:', error);
      this.showError('حدث خطأ في فتح نافذة تسديد الديون');
    }
  }

  async confirmPayment() {
    try {
      if (!this.currentDebtCustomer) return;

      const paymentAmount = parseFloat(document.getElementById('paymentAmount').value);
      const paymentMethod = document.getElementById('paymentMethod').value;
      const paymentNotes = document.getElementById('paymentNotes').value;
      const paymentDate = document.getElementById('paymentDate').value;

      // التحقق من صحة البيانات
      if (!paymentAmount || paymentAmount <= 0) {
        this.showError('يرجى إدخال مبلغ صحيح');
        return;
      }

      if (!paymentMethod) {
        this.showError('يرجى اختيار طريقة الدفع');
        return;
      }

      if (paymentAmount > this.currentDebtCustomer.total_debt) {
        this.showError('المبلغ المدخل أكبر من إجمالي الديون');
        return;
      }

      // تحميل الطلبات غير المسددة
      const unpaidOrders = simpleDatabase.getAll('repair_orders').filter(order =>
        order.customer_id == this.currentDebtCustomer.id && order.remaining_amount > 0
      );

      // توزيع المبلغ على الطلبات
      let remainingPayment = paymentAmount;
      const updatedOrders = [];

      for (const order of unpaidOrders) {
        if (remainingPayment <= 0) break;

        const orderDebt = order.remaining_amount;
        const paymentForOrder = Math.min(remainingPayment, orderDebt);

        const newPaidAmount = (order.paid_amount || 0) + paymentForOrder;
        const newRemainingAmount = order.price - newPaidAmount;

        // تحديث الطلب
        const updateData = {
          paid_amount: newPaidAmount,
          remaining_amount: newRemainingAmount,
          updated_at: new Date().toISOString()
        };

        simpleDatabase.update('repair_orders', order.id, updateData);
        updatedOrders.push({ ...order, ...updateData, payment_amount: paymentForOrder });

        remainingPayment -= paymentForOrder;
      }

      // تسجيل عملية الدفع
      const paymentRecord = {
        customer_id: this.currentDebtCustomer.id,
        amount: paymentAmount,
        payment_method: paymentMethod,
        notes: paymentNotes,
        payment_date: paymentDate || new Date().toISOString(),
        receipt_number: this.generateReceiptNumber(),
        created_at: new Date().toISOString()
      };

      const paymentResult = simpleDatabase.insert('payments', paymentRecord);

      if (paymentResult.id) {
        // تسجيل النشاط
        simpleDatabase.logActivity('payment', 'customers', this.currentDebtCustomer.id,
          null, { amount: paymentAmount, method: paymentMethod });

        // إنشاء وطباعة إيصال التسديد
        await this.generateDebtPaymentReceipt(this.currentDebtCustomer, paymentRecord, updatedOrders);

        this.showSuccess(`تم تسديد ${paymentAmount.toFixed(2)} ريال بنجاح`);
        this.hideModal('payDebtModal');
        await this.loadCustomers();
        this.updateStatsSummary();
      } else {
        this.showError('فشل في تسجيل الدفع');
      }

    } catch (error) {
      console.error('خطأ في تسجيل الدفع:', error);
      this.showError('حدث خطأ في تسجيل الدفع');
    }
  }

  // دوال التصدير والطباعة
  async exportToExcel() {
    try {
      if (typeof XLSX === 'undefined') {
        this.showError('مكتبة Excel غير متوفرة');
        return;
      }

      const data = this.filteredCustomers.map(customer => ({
        'رقم العميل': customer.id,
        'الاسم الثلاثي': customer.name,
        'رقم الهاتف': customer.phone,
        'العنوان': customer.address || '',
        'وسائل التواصل': customer.social_media || '',
        'عدد الطلبات': customer.orders_count,
        'إجمالي المدفوع': customer.total_paid || 0,
        'إجمالي الديون': customer.total_debt,
        'الحالة': this.getCustomerStatusText(customer),
        'تاريخ التسجيل': this.formatDate(customer.created_at),
        'ملاحظات': customer.notes || ''
      }));

      const ws = XLSX.utils.json_to_sheet(data);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'العملاء');

      const fileName = `العملاء_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      this.showSuccess('تم تصدير بيانات العملاء إلى Excel بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      this.showError('حدث خطأ في تصدير البيانات');
    }
  }

  async exportToPDF() {
    try {
      if (typeof jsPDF === 'undefined') {
        this.showError('مكتبة PDF غير متوفرة');
        return;
      }

      const pdf = new jsPDF('p', 'mm', 'a4');

      // إضافة عنوان
      pdf.setFont('Arial', 'bold');
      pdf.setFontSize(16);
      pdf.text('تقرير العملاء', 105, 20, { align: 'center' });

      // إضافة تاريخ التقرير
      pdf.setFontSize(10);
      pdf.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}`, 105, 30, { align: 'center' });

      // إضافة الجدول
      const tableData = this.filteredCustomers.map(customer => [
        customer.id,
        customer.name,
        customer.phone,
        customer.orders_count,
        `${customer.total_debt.toFixed(2)} ريال`,
        this.getCustomerStatusText(customer)
      ]);

      const headers = ['الرقم', 'الاسم', 'الهاتف', 'الطلبات', 'الديون', 'الحالة'];

      // استخدام autoTable إذا كانت متوفرة
      if (typeof pdf.autoTable === 'function') {
        pdf.autoTable({
          head: [headers],
          body: tableData,
          startY: 40,
          styles: { fontSize: 8, cellPadding: 2 },
          headStyles: { fillColor: [37, 99, 235] }
        });
      }

      const fileName = `العملاء_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      this.showSuccess('تم تصدير تقرير العملاء إلى PDF بنجاح');

    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      this.showError('حدث خطأ في تصدير التقرير');
    }
  }

  async printCustomerCards() {
    try {
      const printContent = `
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>بطاقات العملاء</title>
          <style>
            body { font-family: Arial, sans-serif; direction: rtl; margin: 20px; }
            .cards-container { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
            .customer-card {
              border: 2px solid #2563eb;
              border-radius: 10px;
              padding: 20px;
              background: linear-gradient(135deg, #f8fafc, #e2e8f0);
              page-break-inside: avoid;
            }
            .card-header { text-align: center; margin-bottom: 15px; }
            .card-title { color: #2563eb; font-size: 18px; font-weight: bold; margin: 0; }
            .card-subtitle { color: #64748b; font-size: 12px; margin: 5px 0; }
            .card-body { margin: 15px 0; }
            .card-row { display: flex; justify-content: space-between; margin: 8px 0; }
            .card-label { font-weight: bold; color: #374151; }
            .card-value { color: #1f2937; }
            .qr-code { text-align: center; margin-top: 15px; }
            @media print {
              body { margin: 0; }
              .cards-container { grid-template-columns: repeat(2, 1fr); }
            }
          </style>
        </head>
        <body>
          <div class="cards-container">
            ${this.filteredCustomers.map(customer => `
              <div class="customer-card">
                <div class="card-header">
                  <h3 class="card-title">بطاقة عميل</h3>
                  <p class="card-subtitle">مركز صيانة الموبايلات</p>
                </div>
                <div class="card-body">
                  <div class="card-row">
                    <span class="card-label">رقم العميل:</span>
                    <span class="card-value">#${customer.id}</span>
                  </div>
                  <div class="card-row">
                    <span class="card-label">الاسم:</span>
                    <span class="card-value">${customer.name}</span>
                  </div>
                  <div class="card-row">
                    <span class="card-label">الهاتف:</span>
                    <span class="card-value">${customer.phone}</span>
                  </div>
                  <div class="card-row">
                    <span class="card-label">عدد الطلبات:</span>
                    <span class="card-value">${customer.orders_count}</span>
                  </div>
                  <div class="card-row">
                    <span class="card-label">الحالة:</span>
                    <span class="card-value">${this.getCustomerStatusText(customer)}</span>
                  </div>
                </div>
                <div class="qr-code">
                  <p style="font-size: 10px; margin: 5px 0;">رمز العميل: ${customer.id}</p>
                  <div style="border: 1px solid #ccc; width: 60px; height: 60px; margin: 0 auto; display: flex; align-items: center; justify-content: center; font-size: 10px;">
                    QR Code
                  </div>
                </div>
              </div>
            `).join('')}
          </div>
        </body>
        </html>
      `;

      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();

    } catch (error) {
      console.error('خطأ في طباعة البطاقات:', error);
      this.showError('حدث خطأ في طباعة البطاقات');
    }
  }

  // دوال مساعدة
  formatDate(dateString) {
    if (!dateString) return 'غير محدد';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      return 'تاريخ غير صحيح';
    }
  }

  getOrderStatusText(status) {
    const statusTexts = {
      'pending': 'قيد الانتظار',
      'in-progress': 'قيد الصيانة',
      'completed': 'مكتمل',
      'failed': 'فشل الصيانة',
      'waiting-parts': 'بانتظار قطع الغيار',
      'delivered': 'تم التسليم'
    };
    return statusTexts[status] || status;
  }

  getPaymentMethodText(method) {
    const methods = {
      'cash': 'نقداً',
      'partial': 'جزئي',
      'deferred': 'آجل'
    };
    return methods[method] || method;
  }

  generateReceiptNumber() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    return `PAY${timestamp}${random}`;
  }

  async generateDebtPaymentReceipt(customer, payment, updatedOrders) {
    try {
      // الحصول على إعدادات المركز
      const centerName = simpleDatabase.getSetting('center_name') || 'مركز صيانة الموبايلات';
      const centerAddress = simpleDatabase.getSetting('center_address') || 'العنوان';
      const centerPhone = simpleDatabase.getSetting('center_phone') || 'رقم الهاتف';

      const receiptHTML = `
        <div style="max-width: 210mm; margin: 0 auto; padding: 20px; font-family: 'Cairo', Arial, sans-serif; direction: rtl;">
          <!-- رأس الإيصال -->
          <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #dc2626; padding-bottom: 20px;">
            <h1 style="color: #dc2626; margin: 0; font-size: 24px;">${centerName}</h1>
            <p style="margin: 5px 0;">${centerAddress}</p>
            <p style="margin: 5px 0;">${centerPhone}</p>
          </div>

          <!-- عنوان الإيصال -->
          <div style="text-align: center; margin-bottom: 25px;">
            <h2 style="background: #dc2626; color: white; padding: 15px; border-radius: 8px; margin: 0;">
              وصل تسديد ديون
            </h2>
          </div>

          <!-- معلومات الإيصال -->
          <div style="margin-bottom: 20px; padding: 15px; background: #f9fafb; border-radius: 8px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
              <span><strong>رقم الوصل:</strong> <span style="color: #dc2626; font-weight: bold;">${payment.receipt_number}</span></span>
              <span><strong>التاريخ:</strong> ${this.formatDate(payment.payment_date)}</span>
            </div>
          </div>

          <!-- بيانات العميل -->
          <div style="margin-bottom: 20px;">
            <h3 style="background: #dc2626; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">بيانات العميل</h3>
            <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
              <p><strong>رقم العميل:</strong> #${customer.id}</p>
              <p><strong>الاسم:</strong> ${customer.name}</p>
              <p><strong>الهاتف:</strong> ${customer.phone}</p>
            </div>
          </div>

          <!-- تفاصيل الدفع -->
          <div style="margin-bottom: 20px;">
            <h3 style="background: #dc2626; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">تفاصيل التسديد</h3>
            <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
              <p><strong>المبلغ المدفوع:</strong> ${payment.amount.toFixed(2)} ريال</p>
              <p><strong>طريقة الدفع:</strong> ${this.getPaymentMethodText(payment.payment_method)}</p>
              ${payment.notes ? `<p><strong>ملاحظات:</strong> ${payment.notes}</p>` : ''}
            </div>
          </div>

          <!-- الطلبات المسددة -->
          ${updatedOrders.length > 0 ? `
            <div style="margin-bottom: 20px;">
              <h3 style="background: #dc2626; color: white; padding: 10px; margin: 0; border-radius: 8px 8px 0 0;">الطلبات المسددة</h3>
              <div style="padding: 15px; border: 1px solid #e5e7eb; border-top: none; border-radius: 0 0 8px 8px;">
                ${updatedOrders.map(order => `
                  <p>طلب #${order.order_number || order.id}: ${order.payment_amount.toFixed(2)} ريال</p>
                `).join('')}
              </div>
            </div>
          ` : ''}

          <!-- توقيع وختم -->
          <div style="margin-top: 40px; display: flex; justify-content: space-between;">
            <div style="text-align: center;">
              <div style="border-top: 1px solid #000; width: 150px; margin-bottom: 5px;"></div>
              <p style="margin: 0; font-size: 12px;">توقيع الموظف</p>
            </div>
            <div style="text-align: center;">
              <div style="border: 1px solid #000; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 5px;">
                <span style="font-size: 10px;">ختم المركز</span>
              </div>
            </div>
          </div>

          <!-- رسالة شكر -->
          <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #dc2626; font-weight: bold; font-size: 16px;">شكراً لثقتكم بنا</p>
            <p style="color: #6b7280; font-size: 12px;">نتشرف بخدمتكم دائماً</p>
            <p style="font-size: 10px; color: #9ca3af;">تم إنشاء هذا الوصل في: ${new Date().toLocaleString('ar-SA')}</p>
          </div>
        </div>
      `;

      // طباعة الإيصال
      const printWindow = window.open('', '_blank');
      printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
          <meta charset="UTF-8">
          <title>وصل تسديد ديون #${payment.receipt_number}</title>
          <style>
            body { margin: 0; padding: 0; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          ${receiptHTML}
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              };
            };
          </script>
        </body>
        </html>
      `);

      printWindow.document.close();

    } catch (error) {
      console.error('خطأ في إنشاء إيصال التسديد:', error);
      this.showError('حدث خطأ في إنشاء إيصال التسديد');
    }
  }

  // دوال النوافذ المنبثقة
  showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }
  }

  hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
      modal.style.display = 'none';
      document.body.style.overflow = 'auto';
    }
  }

  // دوال الإشعارات
  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="notification-icon icon-${type === 'success' ? 'check' : type === 'error' ? 'warning' : 'info'}"></i>
        <span class="notification-text">${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="icon-close"></i>
      </button>
    `;

    // إضافة الأنماط إذا لم تكن موجودة
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        .notification {
          position: fixed;
          top: 20px;
          right: 20px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          padding: 16px;
          display: flex;
          align-items: center;
          gap: 12px;
          z-index: 10000;
          animation: slideInRight 0.3s ease-out;
          max-width: 400px;
        }
        .notification-success { border-left: 4px solid #10b981; }
        .notification-error { border-left: 4px solid #ef4444; }
        .notification-info { border-left: 4px solid #3b82f6; }
        .notification-content { display: flex; align-items: center; gap: 8px; flex: 1; }
        .notification-icon { font-size: 18px; }
        .notification-success .notification-icon { color: #10b981; }
        .notification-error .notification-icon { color: #ef4444; }
        .notification-close { background: none; border: none; cursor: pointer; color: #6b7280; }
        @keyframes slideInRight { from { transform: translateX(100%); } to { transform: translateX(0); } }
      `;
      document.head.appendChild(style);
    }

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }

  showLoading() {
    const tbody = document.getElementById('customersTableBody');
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="8" style="text-align: center; padding: 40px;">
            <div class="loading-spinner"></div>
            <p>جاري تحميل البيانات...</p>
          </td>
        </tr>
      `;
    }
  }

  hideLoading() {
    // سيتم إخفاء التحميل عند عرض البيانات
  }
}

// دوال عامة للوصول من HTML
window.viewCustomerDetails = function(customerId) {
  if (window.customersManager) {
    window.customersManager.viewCustomerDetails(customerId);
  }
};

window.editCustomer = function(customerId) {
  if (window.customersManager) {
    window.customersManager.editCustomer(customerId);
  }
};

window.deleteCustomer = function(customerId) {
  if (window.customersManager) {
    window.customersManager.deleteCustomer(customerId);
  }
};

window.viewCustomerOrders = function(customerId) {
  if (window.customersManager) {
    window.customersManager.viewCustomerOrders(customerId);
  }
};

window.payCustomerDebt = function(customerId) {
  if (window.customersManager) {
    window.customersManager.payCustomerDebt(customerId);
  }
};

window.printCustomerCard = function(customerId) {
  if (window.customersManager) {
    const customer = window.customersManager.allCustomers.find(c => c.id == customerId);
    if (customer) {
      window.customersManager.filteredCustomers = [customer];
      window.customersManager.printCustomerCards();
    }
  }
};

window.changePage = function(page) {
  if (window.customersManager) {
    window.customersManager.currentPage = page;
    window.customersManager.renderTable();
  }
};

window.clearFilters = function() {
  if (window.customersManager) {
    // مسح جميع الفلاتر
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';

    // إعادة تعيين الفلاتر
    window.customersManager.filters = {
      search: '',
      status: ''
    };

    // تطبيق الفلاتر
    window.customersManager.applyFilters();
    window.customersManager.toggleClearSearchBtn();
  }
};

// دوال للتكامل مع صفحات أخرى
window.viewOrderDetails = function(orderId) {
  // يمكن ربطها مع صفحة عرض الطلبات
  console.log('عرض تفاصيل الطلب:', orderId);
  if (window.viewOrdersManager && window.viewOrdersManager.viewOrderDetails) {
    window.viewOrdersManager.viewOrderDetails(orderId);
  }
};

window.printOrderReceipt = function(orderId) {
  // يمكن ربطها مع صفحة عرض الطلبات
  console.log('طباعة إيصال الطلب:', orderId);
  if (window.viewOrdersManager && window.viewOrdersManager.printOrderReceipt) {
    window.viewOrdersManager.printOrderReceipt(orderId);
  }
};

// تصدير الكلاس
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CustomersManager;
} else {
  window.CustomersManager = CustomersManager;
}
