/* لوحة التحكم الرئيسية */
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: var(--gray-100);
}

/* الشريط الجانبي */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  color: white;
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  padding: 8px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* التنقل */
.sidebar-nav {
  padding: 20px 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 5px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-link:before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-link:hover,
.nav-item.active .nav-link {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-link:hover:before,
.nav-item.active .nav-link:before {
  transform: scaleY(1);
}

.nav-link i {
  font-size: 20px;
  width: 24px;
  text-align: center;
}

.nav-text {
  font-weight: 500;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* تذييل الشريط الجانبي */
.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
  padding: 15px 20px;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #fca5a5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.logout-btn:hover {
  background: rgba(239, 68, 68, 0.3);
  color: white;
}

/* المحتوى الرئيسي */
.main-content {
  flex: 1;
  margin-right: 280px;
  transition: margin-right 0.3s ease;
}

.sidebar.collapsed + .main-content {
  margin-right: 80px;
}

/* الشريط العلوي */
.top-bar {
  background: white;
  padding: 15px 30px;
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.top-bar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--gray-600);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.top-bar-center {
  flex: 1;
  max-width: 500px;
  margin: 0 30px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 2px solid var(--gray-300);
  border-radius: 25px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: var(--gray-50);
}

.search-input:focus {
  border-color: var(--primary-color);
  background: white;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-btn:hover {
  color: var(--primary-color);
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-name {
  font-weight: 500;
  color: var(--gray-700);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

/* منطقة المحتوى */
.content-area {
  padding: 30px;
  min-height: calc(100vh - 80px);
}

/* محتوى لوحة التحكم */
.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-color);
}

.stat-card.stat-primary:before { background: var(--primary-color); }
.stat-card.stat-success:before { background: var(--success-color); }
.stat-card.stat-warning:before { background: var(--warning-color); }
.stat-card.stat-danger:before { background: var(--danger-color); }

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-primary .stat-icon { background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); }
.stat-success .stat-icon { background: linear-gradient(135deg, var(--success-color), #059669); }
.stat-warning .stat-icon { background: linear-gradient(135deg, var(--warning-color), #d97706); }
.stat-danger .stat-icon { background: linear-gradient(135deg, var(--danger-color), #dc2626); }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: var(--gray-800);
  margin: 0 0 5px 0;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--gray-600);
  margin: 0;
  font-weight: 500;
}

/* الأقسام */
.recent-section,
.notifications-section {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: var(--shadow);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--gray-200);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--gray-800);
  margin: 0;
}

.view-all-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.3s ease;
}

.view-all-link:hover {
  color: var(--secondary-color);
}

/* الطلبات الحديثة */
.recent-orders {
  display: grid;
  gap: 15px;
}

.order-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  cursor: pointer;
}

.order-item:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.order-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.order-number {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 16px;
}

.order-customer {
  color: var(--gray-700);
  font-weight: 500;
}

.order-device {
  color: var(--gray-500);
  font-size: 14px;
}

.order-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 80px;
}

.status-pending { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
.status-in-progress { background: rgba(6, 182, 212, 0.1); color: var(--info-color); }
.status-completed { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
.status-failed { background: rgba(239, 68, 68, 0.1); color: var(--danger-color); }

/* الإشعارات */
.notifications-list {
  display: grid;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px;
  border-radius: var(--border-radius);
  background: var(--gray-50);
  transition: background 0.3s ease;
}

.notification-item:hover {
  background: var(--gray-100);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.notification-warning .notification-icon { background: var(--warning-color); }
.notification-info .notification-icon { background: var(--info-color); }
.notification-success .notification-icon { background: var(--success-color); }

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: var(--gray-800);
  margin: 0 0 4px 0;
  font-size: 14px;
}

.notification-text {
  color: var(--gray-600);
  margin: 0;
  font-size: 13px;
}

/* نافذة البحث المنبثقة */
.search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.search-modal-content {
  background: white;
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.search-modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
}

.close-modal {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--gray-500);
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.search-results {
  padding: 20px 25px;
  max-height: 60vh;
  overflow-y: auto;
}

/* الأيقونات */
.icon-dashboard:before { content: '🏠'; }
.icon-plus:before { content: '➕'; }
.icon-list:before { content: '📋'; }
.icon-users:before { content: '👥'; }
.icon-parts:before { content: '🔧'; }
.icon-document:before { content: '📄'; }
.icon-chart:before { content: '📊'; }
.icon-history:before { content: '📜'; }
.icon-settings:before { content: '⚙️'; }
.icon-logout:before { content: '🚪'; }
.icon-menu:before { content: '☰'; }
.icon-search:before { content: '🔍'; }
.icon-user:before { content: '👤'; }
.icon-close:before { content: '✖️'; }
.icon-money:before { content: '💰'; }
.icon-warning:before { content: '⚠️'; }
.icon-info:before { content: 'ℹ️'; }
.icon-bell:before { content: '🔔'; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-right: 0;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .top-bar-center {
    margin: 0 15px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .user-name {
    display: none;
  }
}
