# نظام إدارة صيانة الموبايلات

نظام شامل لإدارة صيانة محل الموبايلات مبني بتقنية Electron لسطح المكتب.

## المميزات الرئيسية

### 🔐 نظام تسجيل الدخول
- واجهة تسجيل دخول آمنة وجميلة
- بيانات الدخول الافتراضية:
  - اسم المستخدم: `abd`
  - كلمة المرور: `ZAin1998`

### 🏠 لوحة التحكم الرئيسية
- إحصائيات شاملة (الطلبات، العملاء، قطع الغيار، الديون)
- خانة بحث شامل في جميع أجزاء النظام
- عرض الطلبات الحديثة
- نظام إشعارات وتنبيهات

### ➕ إضافة طلبات الصيانة
- إضافة عملاء جدد أو اختيار من العملاء الموجودين
- اختيار الشركة المصنعة ونوع الهاتف
- وصف مفصل للمشكلة
- نظام دفع متقدم (نقداً، جزئي، آجل)
- إنشاء إيصالات تلقائية قابلة للطباعة

### 👥 إدارة العملاء
- عرض جميع العملاء وتفاصيلهم
- تتبع طلبات كل عميل
- إدارة الديون والمدفوعات
- وصولات تسديد الديون

### 🔧 إدارة قطع الغيار
- إضافة وتعديل قطع الغيار
- تتبع المخزون والكميات
- استيراد وتصدير البيانات (Excel/PDF)
- نظام باركود

### 📄 المستندات والتقارير
- عرض جميع المستندات المحفوظة
- تقارير شاملة عن الأداء
- سجل شامل لجميع العمليات

### ⚙️ الإعدادات
- تخصيص معلومات المركز
- إعدادات الطباعة (A4/A5/طابعة حرارية)
- النسخ الاحتياطي واستعادة البيانات
- تخصيص الألوان والواجهة

## متطلبات التشغيل

- Windows 10 أو أحدث
- Node.js 16 أو أحدث
- 4 جيجابايت رام على الأقل
- 500 ميجابايت مساحة فارغة

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd الصيانة
```

### 2. تثبيت المكتبات
```bash
npm install
```

### 3. تشغيل التطبيق في وضع التطوير
```bash
npm start
```

### 4. بناء التطبيق للتوزيع
```bash
npm run build
```

## هيكل المشروع

```
الصيانة/
├── main.js                 # الملف الرئيسي لـ Electron
├── package.json            # إعدادات المشروع والمكتبات
├── src/
│   ├── pages/             # صفحات HTML
│   │   ├── login.html     # صفحة تسجيل الدخول
│   │   └── dashboard.html # لوحة التحكم الرئيسية
│   ├── components/        # مكونات قابلة للإعادة
│   │   └── add-repair.html # صفحة إضافة الصيانة
│   ├── css/              # ملفات الأنماط
│   │   ├── global.css    # الأنماط العامة
│   │   ├── login.css     # أنماط تسجيل الدخول
│   │   └── dashboard.css # أنماط لوحة التحكم
│   └── js/               # ملفات JavaScript
│       ├── database.js   # إدارة قاعدة البيانات
│       ├── login.js      # منطق تسجيل الدخول
│       ├── dashboard.js  # منطق لوحة التحكم
│       └── add-repair.js # منطق إضافة الصيانة
├── assets/               # الملفات الثابتة
│   ├── icons/           # الأيقونات
│   └── images/          # الصور
└── database/            # قاعدة البيانات المحلية
    └── repair_system.db # ملف قاعدة البيانات SQLite
```

## قاعدة البيانات

النظام يستخدم قاعدة بيانات SQLite محلية تحتوي على الجداول التالية:

- `settings` - إعدادات النظام
- `customers` - بيانات العملاء
- `manufacturers` - الشركات المصنعة
- `repair_orders` - طلبات الصيانة
- `spare_parts` - قطع الغيار
- `payments` - المدفوعات
- `documents` - المستندات
- `activity_log` - سجل العمليات

## المكتبات المستخدمة

- **Electron** - لبناء تطبيق سطح المكتب
- **SQLite3** - قاعدة البيانات المحلية
- **jsPDF** - إنشاء ملفات PDF
- **html2canvas** - تحويل HTML إلى صور
- **jsbarcode** - إنشاء الباركود
- **qrcode** - إنشاء رموز QR
- **xlsx** - التعامل مع ملفات Excel
- **moment** - التعامل مع التواريخ

## الميزات المتقدمة

### نظام البحث الذكي
- البحث في جميع أجزاء النظام من مكان واحد
- البحث بالاسم، رقم الهاتف، رقم الطلب، الباركود
- نتائج فورية مع التصفية

### نظام الطباعة المتقدم
- دعم أحجام طباعة متعددة (A4, A5, طابعة حرارية)
- قوالب إيصالات قابلة للتخصيص
- طباعة مباشرة أو حفظ كـ PDF

### نظام الإشعارات
- تنبيهات للطلبات المتأخرة
- تنبيهات للمخزون المنخفض
- تنبيهات للديون المستحقة

### النسخ الاحتياطي
- نسخ احتياطي تلقائي لقاعدة البيانات
- استيراد وتصدير البيانات
- حماية من فقدان البيانات

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

1. تحقق من ملف السجل في مجلد التطبيق
2. راجع قسم الأسئلة الشائعة
3. تواصل مع فريق الدعم

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

---

**تم تطوير هذا النظام خصيصاً لمحلات صيانة الموبايلات لتسهيل إدارة العمليات اليومية وتحسين خدمة العملاء.**
