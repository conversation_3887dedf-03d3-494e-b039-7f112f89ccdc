/* صفحة عرض الطلبات */
.view-orders-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
}

/* رأس الصفحة */
.page-header {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border-radius: var(--border-radius-lg);
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.header-text h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
}

.header-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.stats-summary {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 20px;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.9;
}

/* شريط الأدوات */
.toolbar {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.toolbar-left {
  flex: 1;
  min-width: 300px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 12px 50px 12px 20px;
  border: 2px solid var(--gray-300);
  border-radius: 25px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-btn,
.clear-search-btn {
  position: absolute;
  background: none;
  border: none;
  color: var(--gray-500);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.search-btn {
  left: 15px;
}

.clear-search-btn {
  left: 50px;
}

.search-btn:hover,
.clear-search-btn:hover {
  color: var(--primary-color);
  background: var(--gray-100);
}

.toolbar-center {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.filters {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 14px;
  min-width: 120px;
}

.filter-select:focus,
.filter-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.toolbar-right {
  display: flex;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .btn {
  padding: 8px 16px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* الجدول */
.table-container {
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: 20px;
}

.table-wrapper {
  overflow-x: auto;
}

.orders-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.orders-table th {
  background: var(--gray-100);
  padding: 15px 12px;
  text-align: right;
  font-weight: 600;
  color: var(--gray-700);
  border-bottom: 2px solid var(--gray-200);
  white-space: nowrap;
  position: relative;
}

.orders-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background 0.3s ease;
}

.orders-table th.sortable:hover {
  background: var(--gray-200);
}

.sort-icon {
  margin-right: 5px;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.orders-table th.sortable:hover .sort-icon,
.orders-table th.sorted .sort-icon {
  opacity: 1;
}

.orders-table td {
  padding: 12px;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

.orders-table tr:hover {
  background: var(--gray-50);
}

/* أرقام الطلبات */
.order-number {
  font-weight: 700;
  color: #dc2626;
  font-size: 16px;
}

/* حالات الطلبات */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
  min-width: 80px;
}

.status-pending { background: rgba(156, 163, 175, 0.2); color: #6b7280; }
.status-in-progress { background: rgba(245, 158, 11, 0.2); color: #d97706; }
.status-completed { background: rgba(59, 130, 246, 0.2); color: #2563eb; }
.status-failed { background: rgba(239, 68, 68, 0.2); color: #dc2626; }
.status-waiting-parts { background: rgba(107, 114, 128, 0.2); color: #6b7280; }
.status-delivered { background: rgba(16, 185, 129, 0.2); color: #059669; }

/* طرق الدفع */
.payment-method {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.payment-cash { background: rgba(16, 185, 129, 0.1); color: #059669; }
.payment-partial { background: rgba(245, 158, 11, 0.1); color: #d97706; }
.payment-deferred { background: rgba(239, 68, 68, 0.1); color: #dc2626; }

/* المبالغ */
.amount {
  font-weight: 600;
  text-align: left;
  direction: ltr;
}

.amount-paid {
  color: var(--success-color);
}

.amount-remaining {
  color: var(--danger-color);
}

.debt-indicator {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  color: var(--danger-color);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.debt-indicator:hover {
  color: var(--danger-color);
  text-decoration: underline;
}

/* أزرار الإجراءات */
.actions-cell {
  white-space: nowrap;
}

.action-btn {
  padding: 6px 10px;
  margin: 0 2px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.action-btn-view { background: var(--info-color); color: white; }
.action-btn-edit { background: var(--warning-color); color: white; }
.action-btn-delete { background: var(--danger-color); color: white; }
.action-btn-print { background: var(--success-color); color: white; }
.action-btn-status { background: var(--primary-color); color: white; }

/* رسالة عدم وجود بيانات */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: var(--gray-500);
}

.no-data-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-data h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: var(--gray-600);
}

.no-data p {
  margin: 0 0 20px 0;
  font-size: 16px;
}

/* التنقل بين الصفحات */
.pagination-container {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 20px;
  box-shadow: var(--shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.pagination-info {
  color: var(--gray-600);
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.items-per-page {
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 14px;
}

.pagination-buttons {
  display: flex;
  gap: 5px;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid var(--gray-300);
  background: white;
  color: var(--gray-700);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.pagination-btn:hover {
  background: var(--gray-100);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* النوافذ المنبثقة */
.modal-lg {
  max-width: 900px;
}

.order-details {
  display: grid;
  gap: 20px;
}

.detail-section {
  background: var(--gray-50);
  padding: 20px;
  border-radius: var(--border-radius);
}

.detail-section h4 {
  margin: 0 0 15px 0;
  color: var(--gray-800);
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid var(--gray-200);
}

.detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--gray-600);
}

.detail-value {
  color: var(--gray-800);
  font-weight: 500;
}

/* خيارات الحالة */
.status-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.status-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.status-option:hover {
  border-color: var(--primary-color);
  background: var(--gray-50);
}

.status-option input[type="radio"] {
  display: none;
}

.status-option input[type="radio"]:checked + .status-badge {
  box-shadow: 0 0 0 2px var(--primary-color);
}

/* معلومات الدين */
.debt-info {
  background: var(--danger-color);
  color: white;
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
}

.debt-info h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
}

.debt-amount {
  font-size: 24px;
  font-weight: 700;
  margin: 10px 0;
}

/* الأيقونات */
.icon-list:before { content: '📋'; }
.icon-search:before { content: '🔍'; }
.icon-close:before { content: '✖️'; }
.icon-excel:before { content: '📊'; }
.icon-pdf:before { content: '📄'; }
.icon-print:before { content: '🖨'; }
.icon-refresh:before { content: '🔄'; }
.icon-sort:before { content: '↕️'; }
.icon-empty:before { content: '📭'; }
.icon-view:before { content: '👁'; }
.icon-edit:before { content: '✏️'; }
.icon-delete:before { content: '🗑'; }
.icon-status:before { content: '🔄'; }
.icon-money:before { content: '💰'; }
.icon-save:before { content: '💾'; }
