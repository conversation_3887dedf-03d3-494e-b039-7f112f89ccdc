@echo off
echo ========================================
echo    نظام إدارة صيانة الموبايلات
echo ========================================
echo.
echo جاري تشغيل النظام...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من الرابط التالي:
    echo https://nodejs.org/
    pause
    exit /b 1
)

REM التحقق من وجود المكتبات
if not exist "node_modules" (
    echo جاري تثبيت المكتبات المطلوبة...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
)

REM تشغيل التطبيق
echo تم تشغيل النظام بنجاح!
echo.
echo بيانات تسجيل الدخول:
echo اسم المستخدم: abd
echo كلمة المرور: ZAin1998
echo.
npm start

pause
